<?php
// Test script to verify profile data is being saved and retrieved correctly
require_once 'config.php';

echo "<h2>Testing Profile Data Storage and Retrieval</h2>";

try {
    // Check if extended profile columns exist
    echo "<h3>1. Checking Database Columns</h3>";
    $stmt = $pdo->query("DESCRIBE members");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $extended_fields = [
        'bio', 'interests', 'emergency_contact_name', 'emergency_contact_phone',
        'emergency_contact_relationship', 'medical_conditions', 'allergies',
        'preferred_communication', 'anniversary_date', 'baptism_date',
        'volunteer_interests', 'availability_schedule', 'social_media_links'
    ];
    
    echo "<ul>";
    foreach ($extended_fields as $field) {
        $exists = in_array($field, $columns);
        echo "<li>$field: " . ($exists ? "✅ EXISTS" : "❌ MISSING") . "</li>";
    }
    echo "</ul>";
    
    // Get a test member
    echo "<h3>2. Testing Data Retrieval</h3>";
    $stmt = $pdo->query("SELECT id, full_name FROM members LIMIT 1");
    $test_member = $stmt->fetch();
    
    if ($test_member) {
        echo "<p>Test member: {$test_member['full_name']} (ID: {$test_member['id']})</p>";
        
        // Get full member data
        $stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
        $stmt->execute([$test_member['id']]);
        $member_data = $stmt->fetch();
        
        echo "<h4>Extended Profile Data:</h4>";
        echo "<ul>";
        foreach ($extended_fields as $field) {
            $value = $member_data[$field] ?? 'NULL';
            $display_value = empty($value) ? 'EMPTY' : substr($value, 0, 50) . (strlen($value) > 50 ? '...' : '');
            echo "<li><strong>$field:</strong> $display_value</li>";
        }
        echo "</ul>";
        
        // Test updating a field
        echo "<h3>3. Testing Data Update</h3>";
        $test_bio = "Test bio updated at " . date('Y-m-d H:i:s');
        $stmt = $pdo->prepare("UPDATE members SET bio = ? WHERE id = ?");
        $result = $stmt->execute([$test_bio, $test_member['id']]);
        
        if ($result) {
            echo "<p>✅ Successfully updated bio field</p>";
            
            // Verify the update
            $stmt = $pdo->prepare("SELECT bio FROM members WHERE id = ?");
            $stmt->execute([$test_member['id']]);
            $updated_bio = $stmt->fetchColumn();
            
            echo "<p>Updated bio value: <em>$updated_bio</em></p>";
        } else {
            echo "<p>❌ Failed to update bio field</p>";
        }
        
    } else {
        echo "<p>❌ No members found in database</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<h3>4. Instructions</h3>";
echo "<p>1. Update your profile from the user portal</p>";
echo "<p>2. Check the admin view member page</p>";
echo "<p>3. Check the error logs for debugging information</p>";
echo "<p>4. Delete this test file when done: <code>test_profile_data.php</code></p>";
?>
