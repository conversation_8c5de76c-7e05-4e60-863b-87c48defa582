<?php
// <PERSON><PERSON><PERSON> to find members who have filled out their extended profiles
require_once 'config.php';

echo "<h2>🔍 Find Members with Extended Profile Data</h2>";

try {
    // Get all members and check which ones have extended profile data
    $stmt = $pdo->query("SELECT id, full_name, email, bio, interests, emergency_contact_name, medical_conditions, anniversary_date, baptism_date, volunteer_interests FROM members ORDER BY full_name");
    $members = $stmt->fetchAll();
    
    $membersWithData = [];
    $membersWithoutData = [];
    
    foreach ($members as $member) {
        $hasData = false;
        $dataFields = [];
        
        // Check each extended field
        $fields = [
            'bio' => $member['bio'],
            'interests' => $member['interests'], 
            'emergency_contact_name' => $member['emergency_contact_name'],
            'medical_conditions' => $member['medical_conditions'],
            'anniversary_date' => $member['anniversary_date'],
            'baptism_date' => $member['baptism_date'],
            'volunteer_interests' => $member['volunteer_interests']
        ];
        
        foreach ($fields as $fieldName => $fieldValue) {
            if (!empty($fieldValue)) {
                $hasData = true;
                $dataFields[] = $fieldName;
            }
        }
        
        if ($hasData) {
            $member['data_fields'] = $dataFields;
            $membersWithData[] = $member;
        } else {
            $membersWithoutData[] = $member;
        }
    }
    
    echo "<h3>✅ Members WITH Extended Profile Data (" . count($membersWithData) . ")</h3>";
    if (count($membersWithData) > 0) {
        echo "<div style='background: #e8f5e8; padding: 15px; border: 1px solid #4caf50; border-radius: 5px; margin-bottom: 20px;'>";
        echo "<p><strong>These members have filled out their extended profiles and should show data in the admin view:</strong></p>";
        echo "<ul>";
        foreach ($membersWithData as $member) {
            $adminUrl = "admin/view_member.php?id=" . $member['id'];
            echo "<li>";
            echo "<strong><a href='$adminUrl' target='_blank' style='color: #2e7d32; text-decoration: none;'>";
            echo "🎯 {$member['full_name']} (ID: {$member['id']})";
            echo "</a></strong>";
            echo "<br><small>Email: {$member['email']}</small>";
            echo "<br><small>Has data in: " . implode(', ', $member['data_fields']) . "</small>";
            echo "</li><br>";
        }
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<p style='color: orange;'>No members have filled out extended profile data yet.</p>";
    }
    
    echo "<h3>❌ Members WITHOUT Extended Profile Data (" . count($membersWithoutData) . ")</h3>";
    if (count($membersWithoutData) > 0) {
        echo "<div style='background: #ffebee; padding: 15px; border: 1px solid #f44336; border-radius: 5px;'>";
        echo "<p><strong>These members will show 'Not provided' for extended fields:</strong></p>";
        echo "<ul>";
        foreach ($membersWithoutData as $member) {
            echo "<li>{$member['full_name']} (ID: {$member['id']}) - {$member['email']}</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    echo "<h3>🎯 Quick Solution</h3>";
    echo "<div style='background: #e3f2fd; padding: 15px; border: 1px solid #2196f3; border-radius: 5px;'>";
    echo "<p><strong>To see extended profile data in the admin view:</strong></p>";
    echo "<ol>";
    echo "<li>Click on one of the GREEN links above (members WITH data)</li>";
    echo "<li>You should see their extended profile information instead of 'Not provided'</li>";
    echo "<li>The issue is that you were viewing a member who hasn't filled out their profile</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<h3>🧹 Cleanup</h3>";
echo "<p><small>Delete these debug files when done: debug_current_view.php, find_members_with_data.php</small></p>";
?>
