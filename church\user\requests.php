<?php
/**
 * User Requests Management
 * Renamed from Prayer Requests to Requests
 */

require_once '../config.php';
require_once '../includes/automatic_notifications.php';
require_once '../admin/includes/admin_notification_functions.php';
require_once '../includes/enhanced_activity_system.php';

// Ensure prayer_responses table has necessary columns for admin responses
try {
    // Check if is_admin_response column exists
    $stmt = $pdo->query("SHOW COLUMNS FROM prayer_responses LIKE 'is_admin_response'");
    if ($stmt->rowCount() == 0) {
        // Column doesn't exist, add it
        $pdo->exec("ALTER TABLE prayer_responses ADD COLUMN is_admin_response TINYINT(1) DEFAULT 0");
    }

    // Check if admin_id column exists
    $stmt = $pdo->query("SHOW COLUMNS FROM prayer_responses LIKE 'admin_id'");
    if ($stmt->rowCount() == 0) {
        // Column doesn't exist, add it
        $pdo->exec("ALTER TABLE prayer_responses ADD COLUMN admin_id INT NULL");
    }

    // Modify member_id to allow NULL for admin responses
    $pdo->exec("ALTER TABLE prayer_responses MODIFY COLUMN member_id INT NULL");

    // Check if allow_comments column exists in prayer_requests table
    $stmt = $pdo->query("SHOW COLUMNS FROM prayer_requests LIKE 'allow_comments'");
    if ($stmt->rowCount() == 0) {
        // Column doesn't exist, add it with default value of 1 (allow comments)
        $pdo->exec("ALTER TABLE prayer_requests ADD COLUMN allow_comments TINYINT(1) DEFAULT 1");
    }

} catch (PDOException $e) {
    // If there's an error, we'll handle it in the queries below
    error_log("Error checking/adding admin response columns: " . $e->getMessage());
}

// Check if the columns exist for our queries
$has_admin_response_column = false;
$has_admin_id_column = false;
try {
    $stmt = $pdo->query("SHOW COLUMNS FROM prayer_responses LIKE 'is_admin_response'");
    $has_admin_response_column = ($stmt->rowCount() > 0);

    $stmt = $pdo->query("SHOW COLUMNS FROM prayer_responses LIKE 'admin_id'");
    $has_admin_id_column = ($stmt->rowCount() > 0);
} catch (PDOException $e) {
    $has_admin_response_column = false;
    $has_admin_id_column = false;
}

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include classes
require_once '../classes/SecurityManager.php';
require_once '../classes/UserAuthManager.php';
require_once '../includes/enhanced_activity_system.php';

// Include language system for user portal
require_once 'includes/language.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Set security headers
$security->setSecurityHeaders();

// Check if user is authenticated
if (!$userAuth->isAuthenticated()) {
    header("Location: login.php");
    exit();
}

$userId = $_SESSION['user_id'];

// Get current user data
$stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$userData = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$userData) {
    header('Location: login.php');
    exit();
}

$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['create_request'])) {
            // Create new request
            $stmt = $pdo->prepare("
                INSERT INTO prayer_requests (member_id, title, description, category, privacy_level, is_urgent, is_anonymous)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $_SESSION['user_id'],
                $_POST['title'],
                $_POST['description'],
                $_POST['category'],
                $_POST['privacy_level'],
                isset($_POST['is_urgent']) ? 1 : 0,
                isset($_POST['is_anonymous']) ? 1 : 0
            ]);

            $requestId = $pdo->lastInsertId();
            $message = "Request submitted successfully!";

            // Log prayer request creation activity
            global $activityTracker;
            $activityTracker->logActivity(
                $_SESSION['user_id'],
                'prayer_request_created',
                "Created prayer request: " . $_POST['title'],
                'prayer_request',
                $requestId,
                null,
                [
                    'request_title' => $_POST['title'],
                    'category' => $_POST['category'],
                    'privacy_level' => $_POST['privacy_level'],
                    'is_urgent' => isset($_POST['is_urgent']) ? 1 : 0,
                    'is_anonymous' => isset($_POST['is_anonymous']) ? 1 : 0
                ]
            );

            // Send automatic notification to other members
            notifyNewRequestCreated(
                $pdo,
                $_SESSION['user_id'],
                $requestId,
                $_POST['title'],
                $_POST['category'],
                $_POST['privacy_level'],
                isset($_POST['is_urgent'])
            );

            // Notify all admins about new member request
            try {
                // Get member name for notification
                $stmt = $pdo->prepare("SELECT full_name FROM members WHERE id = ?");
                $stmt->execute([$_SESSION['user_id']]);
                $memberName = $stmt->fetchColumn() ?: 'A member';

                // Get all admin IDs
                $stmt = $pdo->prepare("SELECT id FROM admins");
                $stmt->execute();
                $adminIds = $stmt->fetchAll(PDO::FETCH_COLUMN);

                $urgentText = isset($_POST['is_urgent']) ? ' [URGENT]' : '';
                $notificationTitle = "New Member Request Posted" . $urgentText;
                $notificationMessage = "$memberName has posted a new request: \"" . $_POST['title'] . "\" in category: " . $_POST['category'];
                $actionUrl = "../admin/requests.php?id=" . $requestId;
                $priority = isset($_POST['is_urgent']) ? 'high' : 'normal';

                foreach ($adminIds as $adminId) {
                    createAdminNotification(
                        $pdo,
                        $adminId,
                        $notificationTitle,
                        $notificationMessage,
                        'member_activity',
                        $_SESSION['user_id'],
                        'member',
                        $actionUrl,
                        $priority
                    );
                }
            } catch (Exception $e) {
                error_log("Error creating admin notification for new request: " . $e->getMessage());
            }
        }
        
        if (isset($_POST['update_status'])) {
            // Update request status
            $stmt = $pdo->prepare("
                UPDATE prayer_requests 
                SET status = ?, answered_description = ?, answered_date = ?
                WHERE id = ? AND member_id = ?
            ");
            
            $answered_date = $_POST['status'] === 'answered' ? date('Y-m-d H:i:s') : null;
            
            $stmt->execute([
                $_POST['status'],
                $_POST['answered_description'] ?? null,
                $answered_date,
                $_POST['request_id'],
                $_SESSION['user_id']
            ]);
            
            $message = "Request status updated successfully!";
        }
        
        if (isset($_POST['add_response'])) {
            // Add response to someone's request
            $stmt = $pdo->prepare("
                INSERT INTO prayer_responses (prayer_request_id, member_id, response_type, comment)
                VALUES (?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE 
                response_type = VALUES(response_type), 
                comment = VALUES(comment),
                created_at = CURRENT_TIMESTAMP
            ");
            
            $stmt->execute([
                $_POST['request_id'],
                $_SESSION['user_id'],
                $_POST['response_type'],
                $_POST['comment']
            ]);

            $message = "Response added successfully!";

            // Get request information for notification
            $stmt = $pdo->prepare("SELECT member_id, title FROM prayer_requests WHERE id = ?");
            $stmt->execute([$_POST['request_id']]);
            $request = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($request) {
                // Send notification to request owner
                notifyNewRequestComment(
                    $pdo,
                    $_SESSION['user_id'],
                    $_POST['request_id'],
                    $request['member_id'],
                    $request['title'],
                    $_POST['comment']
                );
            }
        }
        
    } catch (PDOException $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Pagination parameters for My Requests
$myPage = max(1, intval($_GET['my_page'] ?? 1));
$myRequestsPerPage = 5;
$myOffset = ($myPage - 1) * $myRequestsPerPage;

// Get total count for My Requests pagination
if ($has_admin_response_column) {
    $countStmt = $pdo->prepare("
        SELECT COUNT(DISTINCT pr.id)
        FROM prayer_requests pr
        WHERE pr.member_id = ?
    ");
} else {
    $countStmt = $pdo->prepare("
        SELECT COUNT(DISTINCT pr.id)
        FROM prayer_requests pr
        WHERE pr.member_id = ?
    ");
}
$countStmt->execute([$_SESSION['user_id']]);
$totalMyRequests = $countStmt->fetchColumn();
$totalMyPages = ceil($totalMyRequests / $myRequestsPerPage);

// Get user's own requests with pagination
if ($has_admin_response_column) {
    $stmt = $pdo->prepare("
        SELECT pr.*,
               COUNT(DISTINCT prr.id) as response_count,
               COUNT(DISTINCT CASE WHEN prr.is_admin_response = 1 OR prr.admin_id IS NOT NULL THEN prr.id END) as admin_response_count,
               MAX(CASE WHEN prr.is_admin_response = 1 OR prr.admin_id IS NOT NULL THEN prr.created_at END) as last_admin_response
        FROM prayer_requests pr
        LEFT JOIN prayer_responses prr ON pr.id = prr.prayer_request_id
        WHERE pr.member_id = ?
        GROUP BY pr.id
        ORDER BY pr.created_at DESC
        LIMIT ? OFFSET ?
    ");
    $stmt->execute([$_SESSION['user_id'], $myRequestsPerPage, $myOffset]);
} else {
    // Fallback query without admin response column
    $stmt = $pdo->prepare("
        SELECT pr.*,
               COUNT(DISTINCT prr.id) as response_count,
               0 as admin_response_count,
               NULL as last_admin_response
        FROM prayer_requests pr
        LEFT JOIN prayer_responses prr ON pr.id = prr.prayer_request_id
        WHERE pr.member_id = ?
        GROUP BY pr.id
        ORDER BY pr.created_at DESC
        LIMIT ? OFFSET ?
    ");
    $stmt->execute([$_SESSION['user_id'], $myRequestsPerPage, $myOffset]);
}
$my_requests = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Pagination parameters for Community Requests
$communityPage = max(1, intval($_GET['community_page'] ?? 1));
$communityRequestsPerPage = 8;
$communityOffset = ($communityPage - 1) * $communityRequestsPerPage;

// Get total count for Community Requests pagination
$countStmt = $pdo->prepare("
    SELECT COUNT(DISTINCT pr.id)
    FROM prayer_requests pr
    JOIN members m ON pr.member_id = m.id
    WHERE pr.member_id != ?
    AND pr.privacy_level IN ('members', 'public')
    AND pr.status IN ('active', 'answered')
");
$countStmt->execute([$_SESSION['user_id']]);
$totalCommunityRequests = $countStmt->fetchColumn();
$totalCommunityPages = ceil($totalCommunityRequests / $communityRequestsPerPage);

// Get community requests (based on privacy level) - Enhanced query with pagination
$stmt = $pdo->prepare("
    SELECT pr.*, m.full_name, m.first_name, m.last_name,
           COUNT(DISTINCT prr.id) as response_count,
           MAX(CASE WHEN prr.member_id = ? THEN 1 ELSE 0 END) as i_responded,
           MAX(prr.created_at) as last_response_time
    FROM prayer_requests pr
    JOIN members m ON pr.member_id = m.id
    LEFT JOIN prayer_responses prr ON pr.id = prr.prayer_request_id
    WHERE pr.member_id != ?
    AND pr.privacy_level IN ('members', 'public')
    AND pr.status IN ('active', 'answered')
    GROUP BY pr.id
    ORDER BY pr.is_urgent DESC, pr.created_at DESC
    LIMIT ? OFFSET ?
");
$stmt->execute([$_SESSION['user_id'], $_SESSION['user_id'], $communityRequestsPerPage, $communityOffset]);
$community_requests = $stmt->fetchAll(PDO::FETCH_ASSOC);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Requests - <?php echo get_organization_name(); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">

    <!-- Custom Theme CSS from Appearance Settings -->
    <?php include 'includes/theme_css.php'; ?>

    <style>
        body {
            background-color: var(--bs-body-bg, #f8f9fa);
            color: var(--bs-body-color, #212529);
            font-family: var(--bs-font-sans-serif, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif);
        }

        .navbar {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 700;
            color: white !important;
            display: flex;
            align-items: center;
        }

        .navbar-logo {
            max-height: 40px;
            max-width: 150px;
            height: auto;
            width: auto;
            object-fit: contain;
        }

        /* Responsive logo adjustments */
        @media (max-width: 768px) {
            .navbar-logo {
                max-height: 32px;
                max-width: 120px;
            }

            .navbar-brand {
                font-size: 1rem;
            }
        }

        .navbar-nav .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .navbar-nav .nav-link:hover {
            color: white !important;
        }

        .dashboard-container {
            margin-top: 2rem;
        }

        .dashboard-card {
            background: var(--bs-body-bg, white);
            border-radius: var(--bs-border-radius, 15px);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border: none;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .dashboard-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.12);
        }

        .dashboard-card h5 {
            color: var(--bs-body-color, #2c3e50);
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            border: none;
            border-radius: var(--bs-border-radius, 10px);
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
        }

        .alert-success {
            border-radius: var(--bs-border-radius, 12px);
            border: none;
            background-color: var(--bs-success, #d4edda);
            color: var(--bs-body-color, #155724);
        }

        .request-item {
            background: var(--bs-body-bg, white);
            border-radius: var(--bs-border-radius, 10px);
            padding: 1rem;
            margin-bottom: 1rem;
            border-left: 4px solid var(--bs-primary, #667eea);
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .request-meta {
            font-size: 0.875rem;
            color: var(--bs-secondary, #6c757d);
        }

        /* Status badges - override navbar badge styles */
        .request-card .badge,
        .request-item .badge,
        .badge.bg-danger,
        .badge.bg-secondary,
        .badge.bg-success,
        .badge.bg-warning,
        .badge.bg-info {
            border-radius: 6px !important;
            padding: 0.25rem 0.5rem !important;
            font-weight: 500 !important;
            font-size: 0.75rem !important;
            line-height: 1.2 !important;
            min-width: auto !important;
            height: auto !important;
            display: inline-block !important;
        }

        /* Sticky navigation - ensure it works properly */
        .navbar {
            position: sticky !important;
            top: 0 !important;
            z-index: 1030 !important;
            transition: box-shadow 0.3s ease;
            width: 100% !important;
        }

        .navbar.scrolled {
            box-shadow: 0 4px 20px rgba(0,0,0,0.15) !important;
            backdrop-filter: blur(10px);
        }

        /* Ensure body has proper margin for sticky header */
        body {
            padding-top: 0;
        }

        .request-card {
            border-left: 4px solid var(--bs-primary, #667eea);
            margin-bottom: 1rem;
        }
        .urgent-request {
            border-left-color: #dc3545;
        }
        .answered-request {
            border-left-color: #28a745;
            opacity: 0.8;
        }
        .closed-request {
            border-left-color: #6c757d;
            opacity: 0.6;
        }
        .response-count {
            background: #e9ecef;
            border-radius: 15px;
            padding: 2px 8px;
            font-size: 0.8rem;
        }

        /* Pagination Styles */
        .pagination .page-link {
            border: none;
            color: var(--bs-primary, #667eea);
            background: transparent;
            margin: 0 2px;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .pagination .page-link:hover {
            background: var(--bs-primary, #667eea);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
        }

        .pagination .page-item.active .page-link {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            border: none;
            color: white;
            box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
        }

        .pagination .page-item.disabled .page-link {
            color: var(--bs-gray-400, #6c757d);
            background: transparent;
        }

        .pagination-info {
            color: var(--bs-gray-600, #6c757d);
            font-size: 0.875rem;
            margin-bottom: 1rem;
        }

        /* Comments View Modal Styling */
        .comments-container {
            max-height: 400px;
            overflow-y: auto;
        }

        .comment-item {
            transition: all 0.3s ease;
        }

        .comment-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .comment-content {
            line-height: 1.6;
        }

        .comment-item.bg-light {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
        }

        /* Enhanced button styling for comments */
        .btn-outline-info:hover {
            background-color: #0dcaf0;
            border-color: #0dcaf0;
            color: white;
        }

        /* Comment count badge in cards */
        .response-count {
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
            border-radius: 15px;
            padding: 4px 10px;
            font-size: 0.8rem;
            font-weight: 500;
            color: #495057;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <div class="container dashboard-container">
        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <!-- Create New Request -->
            <div class="col-md-4">
                <div class="dashboard-card">
                    <h5><i class="bi bi-plus-circle"></i> Submit New Request</h5>
                        <form method="POST">
                            <div class="mb-3">
                                <label for="title" class="form-label">Title *</label>
                                <input type="text" class="form-control" id="title" name="title" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">Description *</label>
                                <textarea class="form-control" id="description" name="description" rows="4" required></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="category" class="form-label">Category</label>
                                <select class="form-select" id="category" name="category">
                                    <option value="personal">Personal</option>
                                    <option value="family">Family</option>
                                    <option value="health">Health</option>
                                    <option value="work">Work</option>
                                    <option value="ministry">Ministry</option>
                                    <option value="community">Community</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="privacy_level" class="form-label">Privacy Level</label>
                                <select class="form-select" id="privacy_level" name="privacy_level">
                                    <option value="private">Private (Only Me)</option>
                                    <option value="members" selected>Members (All Church Members)</option>
                                    <option value="public">Public (Everyone)</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_urgent" name="is_urgent">
                                    <label class="form-check-label" for="is_urgent">
                                        Mark as Urgent
                                    </label>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_anonymous" name="is_anonymous">
                                    <label class="form-check-label" for="is_anonymous">
                                        Submit Anonymously
                                    </label>
                                </div>
                            </div>
                            
                            <button type="submit" name="create_request" class="btn btn-primary">
                                <i class="bi bi-send"></i> Submit Request
                            </button>
                        </form>
                </div>
            </div>

            <!-- My Requests -->
            <div class="col-md-8">
                <div class="dashboard-card">
                    <h5><i class="bi bi-person-circle"></i> My Requests (<?php echo $totalMyRequests; ?>)</h5>
                        <?php if (empty($my_requests)): ?>
                            <p class="text-muted text-center">You haven't submitted any requests yet.</p>
                        <?php else: ?>
                            <?php foreach ($my_requests as $request): ?>
                                <div class="card request-card <?php 
                                    echo $request['is_urgent'] ? 'urgent-request' : '';
                                    echo $request['status'] === 'answered' ? ' answered-request' : '';
                                    echo $request['status'] === 'closed' ? ' closed-request' : '';
                                ?>">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div class="flex-grow-1">
                                                <h6 class="card-title">
                                                    <?php echo htmlspecialchars($request['title']); ?>
                                                    <?php if ($request['is_urgent']): ?>
                                                        <span class="badge bg-danger ms-1">Urgent</span>
                                                    <?php endif; ?>
                                                    <span class="badge bg-secondary ms-1"><?php echo ucfirst($request['status']); ?></span>
                                                </div>
                                            <div class="text-end">
                                                <small class="text-muted"><?php echo date('M j, Y', strtotime($request['created_at'])); ?></small>
                                                <br>
                                                <span class="response-count">
                                                    <i class="bi bi-heart"></i> <?php echo $request['response_count']; ?>
                                                </span>
                                            </div>
                                        </div>
                                        
                                        <p class="card-text mt-2"><?php echo nl2br(htmlspecialchars($request['description'])); ?></p>
                                        
                                        <!-- Request Metadata -->
                                        <div class="mb-3">
                                            <small class="text-muted">
                                                <i class="bi bi-tag"></i> <?php echo ucfirst($request['category']); ?> |
                                                <i class="bi bi-eye"></i> <?php echo ucfirst($request['privacy_level']); ?>
                                            </small>
                                        </div>

                                        <!-- Action Buttons -->
                                        <div class="d-flex gap-2 flex-wrap">
                                            <?php if ($request['status'] === 'active'): ?>
                                                <button class="btn btn-sm btn-outline-success" onclick="updateStatus(<?php echo $request['id']; ?>, 'answered')">
                                                    <i class="bi bi-check-circle"></i> Mark Answered
                                                </button>
                                                <button class="btn btn-sm btn-outline-secondary" onclick="updateStatus(<?php echo $request['id']; ?>, 'closed')">
                                                    <i class="bi bi-x-circle"></i> Close
                                                </button>
                                            <?php endif; ?>

                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteRequest(<?php echo $request['id']; ?>)">
                                                <i class="bi bi-trash"></i> Delete
                                            </button>
                                        </div>
                                        
                                        <?php if ($request['status'] === 'answered' && $request['answered_description']): ?>
                                            <div class="mt-2 p-2 bg-light rounded">
                                                <small><strong>Answer:</strong> <?php echo nl2br(htmlspecialchars($request['answered_description'])); ?></small>
                                            </div>
                                        <?php endif; ?>

                                        <!-- Comments and Admin Responses Section -->
                                        <div class="mt-2">
                                            <div class="d-flex gap-2 flex-wrap">
                                                <?php if ($request['response_count'] > 0): ?>
                                                    <button class="btn btn-sm btn-outline-info" onclick="viewRequestComments(<?php echo $request['id']; ?>)">
                                                        <i class="bi bi-chat-dots"></i> View Comments (<?php echo $request['response_count']; ?>)
                                                    </button>
                                                <?php endif; ?>

                                                <?php if ($request['admin_response_count'] > 0): ?>
                                                    <button class="btn btn-sm btn-outline-primary" onclick="showAdminResponses(<?php echo $request['id']; ?>)">
                                                        <i class="bi bi-person-badge"></i> Admin Response<?php echo $request['admin_response_count'] > 1 ? 's' : ''; ?> (<?php echo $request['admin_response_count']; ?>)
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>

                            <!-- My Requests Pagination -->
                            <?php if ($totalMyPages > 1): ?>
                                <!-- Pagination Info -->
                                <div class="pagination-info text-center mt-4">
                                    Showing <?php echo min($myOffset + 1, $totalMyRequests); ?>-<?php echo min($myOffset + $myRequestsPerPage, $totalMyRequests); ?> of <?php echo $totalMyRequests; ?> requests
                                </div>

                                <div class="d-flex justify-content-center mt-3">
                                    <nav aria-label="My Requests pagination">
                                        <ul class="pagination">
                                            <!-- Previous Page -->
                                            <?php if ($myPage > 1): ?>
                                                <li class="page-item">
                                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['my_page' => $myPage - 1])); ?>">
                                                        <i class="bi bi-chevron-left"></i> Previous
                                                    </a>
                                                </li>
                                            <?php else: ?>
                                                <li class="page-item disabled">
                                                    <span class="page-link">
                                                        <i class="bi bi-chevron-left"></i> Previous
                                                    </span>
                                                </li>
                                            <?php endif; ?>

                                            <!-- Page Numbers -->
                                            <?php
                                            $startPage = max(1, $myPage - 2);
                                            $endPage = min($totalMyPages, $myPage + 2);

                                            if ($startPage > 1): ?>
                                                <li class="page-item">
                                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['my_page' => 1])); ?>">1</a>
                                                </li>
                                                <?php if ($startPage > 2): ?>
                                                    <li class="page-item disabled">
                                                        <span class="page-link">...</span>
                                                    </li>
                                                <?php endif; ?>
                                            <?php endif; ?>

                                            <?php for ($i = $startPage; $i <= $endPage; $i++): ?>
                                                <li class="page-item <?php echo $i === $myPage ? 'active' : ''; ?>">
                                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['my_page' => $i])); ?>"><?php echo $i; ?></a>
                                                </li>
                                            <?php endfor; ?>

                                            <?php if ($endPage < $totalMyPages): ?>
                                                <?php if ($endPage < $totalMyPages - 1): ?>
                                                    <li class="page-item disabled">
                                                        <span class="page-link">...</span>
                                                    </li>
                                                <?php endif; ?>
                                                <li class="page-item">
                                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['my_page' => $totalMyPages])); ?>"><?php echo $totalMyPages; ?></a>
                                                </li>
                                            <?php endif; ?>

                                            <!-- Next Page -->
                                            <?php if ($myPage < $totalMyPages): ?>
                                                <li class="page-item">
                                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['my_page' => $myPage + 1])); ?>">
                                                        Next <i class="bi bi-chevron-right"></i>
                                                    </a>
                                                </li>
                                            <?php else: ?>
                                                <li class="page-item disabled">
                                                    <span class="page-link">
                                                        Next <i class="bi bi-chevron-right"></i>
                                                    </span>
                                                </li>
                                            <?php endif; ?>
                                        </ul>
                                    </nav>
                                </div>
                            <?php endif; ?>

                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Community Requests Section -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="dashboard-card">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5><i class="bi bi-people"></i> Community Requests (<?php echo $totalCommunityRequests; ?>)</h5>
                        <small class="text-muted">Requests from other church members</small>
                    </div>

                    <?php if (empty($community_requests)): ?>
                        <div class="text-center py-4">
                            <i class="bi bi-heart display-4 text-muted"></i>
                            <h6 class="mt-2 text-muted">No community requests at the moment</h6>
                            <p class="text-muted">Check back later to see requests from other church members.</p>
                        </div>
                    <?php else: ?>
                        <div class="row">
                            <?php foreach ($community_requests as $request): ?>
                                <div class="col-lg-4 col-md-6 mb-3">
                                    <div class="card request-card h-100 <?php
                                        echo $request['is_urgent'] ? 'urgent-request' : '';
                                        echo $request['status'] === 'answered' ? ' answered-request' : '';
                                    ?>" id="request-<?php echo $request['id']; ?>">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <div class="flex-grow-1">
                                                    <h6 class="card-title">
                                                        <?php echo htmlspecialchars($request['title']); ?>
                                                        <?php if ($request['is_urgent']): ?>
                                                            <span class="badge bg-danger ms-1">Urgent</span>
                                                        <?php endif; ?>
                                                        <?php if ($request['status'] === 'answered'): ?>
                                                            <span class="badge bg-success ms-1">Answered</span>
                                                        <?php endif; ?>
                                                    </h6>
                                                    <small class="text-muted">
                                                        <i class="bi bi-person"></i>
                                                        <?php echo $request['is_anonymous'] ? 'Anonymous' : htmlspecialchars($request['full_name']); ?>
                                                    </small>
                                                </div>
                                                <small class="text-muted"><?php echo date('M j, Y', strtotime($request['created_at'])); ?></small>
                                            </div>

                                            <p class="card-text"><?php echo nl2br(htmlspecialchars(substr($request['description'], 0, 150))); ?><?php echo strlen($request['description']) > 150 ? '...' : ''; ?></p>

                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <div>
                                                    <span class="badge bg-secondary"><?php echo ucfirst($request['category']); ?></span>
                                                    <span class="badge bg-primary"><?php echo ucfirst($request['privacy_level']); ?></span>
                                                </div>
                                                <div class="text-end">
                                                    <span class="response-count">
                                                        <i class="bi bi-chat-dots"></i> <?php echo $request['response_count']; ?>
                                                    </span>
                                                    <?php if ($request['last_response_time']): ?>
                                                        <br><small class="text-muted">Last: <?php echo date('M j', strtotime($request['last_response_time'])); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>

                                            <div class="d-flex gap-1 flex-wrap">
                                                <button class="btn btn-sm btn-outline-primary" onclick="viewRequestDetails(<?php echo $request['id']; ?>)">
                                                    <i class="bi bi-eye"></i> View Details
                                                </button>
                                                <?php if ($request['response_count'] > 0): ?>
                                                    <button class="btn btn-sm btn-outline-info" onclick="viewRequestComments(<?php echo $request['id']; ?>)">
                                                        <i class="bi bi-chat-dots"></i> View Comments (<?php echo $request['response_count']; ?>)
                                                    </button>
                                                <?php endif; ?>
                                                <?php if ($request['allow_comments']): ?>
                                                    <button class="btn btn-sm btn-outline-success" onclick="showCommentForm(<?php echo $request['id']; ?>)">
                                                        <i class="bi bi-chat-plus"></i> Add Comment
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <!-- Community Requests Pagination -->
                        <?php if ($totalCommunityPages > 1): ?>
                            <!-- Pagination Info -->
                            <div class="pagination-info text-center mt-4">
                                Showing <?php echo min($communityOffset + 1, $totalCommunityRequests); ?>-<?php echo min($communityOffset + $communityRequestsPerPage, $totalCommunityRequests); ?> of <?php echo $totalCommunityRequests; ?> requests
                            </div>

                            <div class="d-flex justify-content-center mt-3">
                                <nav aria-label="Community Requests pagination">
                                    <ul class="pagination">
                                        <!-- Previous Page -->
                                        <?php if ($communityPage > 1): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['community_page' => $communityPage - 1])); ?>">
                                                    <i class="bi bi-chevron-left"></i> Previous
                                                </a>
                                            </li>
                                        <?php else: ?>
                                            <li class="page-item disabled">
                                                <span class="page-link">
                                                    <i class="bi bi-chevron-left"></i> Previous
                                                </span>
                                            </li>
                                        <?php endif; ?>

                                        <!-- Page Numbers -->
                                        <?php
                                        $startPage = max(1, $communityPage - 2);
                                        $endPage = min($totalCommunityPages, $communityPage + 2);

                                        if ($startPage > 1): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['community_page' => 1])); ?>">1</a>
                                            </li>
                                            <?php if ($startPage > 2): ?>
                                                <li class="page-item disabled">
                                                    <span class="page-link">...</span>
                                                </li>
                                            <?php endif; ?>
                                        <?php endif; ?>

                                        <?php for ($i = $startPage; $i <= $endPage; $i++): ?>
                                            <li class="page-item <?php echo $i === $communityPage ? 'active' : ''; ?>">
                                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['community_page' => $i])); ?>"><?php echo $i; ?></a>
                                            </li>
                                        <?php endfor; ?>

                                        <?php if ($endPage < $totalCommunityPages): ?>
                                            <?php if ($endPage < $totalCommunityPages - 1): ?>
                                                <li class="page-item disabled">
                                                    <span class="page-link">...</span>
                                                </li>
                                            <?php endif; ?>
                                            <li class="page-item">
                                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['community_page' => $totalCommunityPages])); ?>"><?php echo $totalCommunityPages; ?></a>
                                            </li>
                                        <?php endif; ?>

                                        <!-- Next Page -->
                                        <?php if ($communityPage < $totalCommunityPages): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['community_page' => $communityPage + 1])); ?>">
                                                    Next <i class="bi bi-chevron-right"></i>
                                                </a>
                                            </li>
                                        <?php else: ?>
                                            <li class="page-item disabled">
                                                <span class="page-link">
                                                    Next <i class="bi bi-chevron-right"></i>
                                                </span>
                                            </li>
                                        <?php endif; ?>
                                    </ul>
                                </nav>
                            </div>
                        <?php endif; ?>

                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Request Details Modal -->
    <div class="modal fade" id="requestDetailsModal" tabindex="-1" aria-labelledby="requestDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="requestDetailsModalLabel">
                        <i class="bi bi-eye"></i> Request Details
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="requestDetailsContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Comment Modal -->
    <div class="modal fade" id="commentModal" tabindex="-1" aria-labelledby="commentModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="commentModalLabel">
                        <i class="bi bi-chat-plus"></i> Add Comment
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="commentForm">
                        <input type="hidden" id="commentRequestId" name="request_id">
                        <div class="mb-3">
                            <label for="responseType" class="form-label">Response Type</label>
                            <select class="form-select" id="responseType" name="response_type">
                                <option value="support">Support & Encouragement</option>
                                <option value="prayer">Prayer</option>
                                <option value="guidance">Guidance</option>
                                <option value="offer_help">Offer to Help</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="commentText" class="form-label">Your Comment</label>
                            <textarea class="form-control" id="commentText" name="comment" rows="4" required placeholder="Share your thoughts, prayers, or offer support..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="submitComment(this)">
                        <i class="bi bi-send"></i> Post Comment
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Comments View Modal -->
    <div class="modal fade" id="commentsViewModal" tabindex="-1" aria-labelledby="commentsViewModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="commentsViewModalLabel">
                        <i class="bi bi-chat-dots"></i> Member Comments
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="commentsViewContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading comments...</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" id="addCommentFromView" onclick="showCommentFormFromView()">
                        <i class="bi bi-chat-plus"></i> Add Comment
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Admin Responses Modal -->
    <div class="modal fade" id="adminResponsesModal" tabindex="-1" aria-labelledby="adminResponsesModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="adminResponsesModalLabel">
                        <i class="bi bi-chat-dots"></i> Admin Responses
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="adminResponsesContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    // Enhanced request functionality
    function viewRequestDetails(requestId) {
        const modal = new bootstrap.Modal(document.getElementById('requestDetailsModal'));
        const content = document.getElementById('requestDetailsContent');

        // Show loading spinner
        content.innerHTML = `
            <div class="text-center">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        `;

        modal.show();

        // Fetch request details and comments
        fetch(`ajax/get_request_details.php?request_id=${requestId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayRequestDetails(data.request, data.comments);
                } else {
                    content.innerHTML = '<p class="text-danger">Error loading request details.</p>';
                }
            })
            .catch(error => {
                content.innerHTML = '<p class="text-danger">Error loading request details.</p>';
            });
    }

    function displayRequestDetails(request, comments) {
        const content = document.getElementById('requestDetailsContent');

        let html = `
            <div class="request-details">
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <div>
                        <h5>${escapeHtml(request.title)}</h5>
                        <small class="text-muted">
                            <i class="bi bi-person"></i> ${request.is_anonymous ? 'Anonymous' : escapeHtml(request.full_name)}
                            • <i class="bi bi-calendar"></i> ${new Date(request.created_at).toLocaleDateString()}
                            • <i class="bi bi-tag"></i> ${request.category}
                        </small>
                    </div>
                    <div class="text-end">
                        ${request.is_urgent ? '<span class="badge bg-danger">Urgent</span>' : ''}
                        <span class="badge bg-${request.status === 'answered' ? 'success' : 'warning'}">${request.status}</span>
                    </div>
                </div>

                <div class="mb-4">
                    <h6>Description:</h6>
                    <p>${escapeHtml(request.description).replace(/\\n/g, '<br>')}</p>
                </div>

                ${request.answered_description ? `
                    <div class="mb-4 p-3 bg-light rounded">
                        <h6><i class="bi bi-check-circle text-success"></i> Answer:</h6>
                        <p>${escapeHtml(request.answered_description).replace(/\\n/g, '<br>')}</p>
                    </div>
                ` : ''}

                <div class="comments-section">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6><i class="bi bi-chat-dots"></i> Comments (${comments.length})</h6>
                        ${request.allow_comments ? `
                            <button class="btn btn-sm btn-primary" onclick="showCommentForm(${request.id})">
                                <i class="bi bi-chat-plus"></i> Add Comment
                            </button>
                        ` : ''}
                    </div>

                    <div id="comments-list-${request.id}">
                        ${displayComments(comments)}
                    </div>
                </div>
            </div>
        `;

        content.innerHTML = html;
    }

    function displayComments(comments) {
        if (comments.length === 0) {
            return '<p class="text-muted text-center">No comments yet. Be the first to comment!</p>';
        }

        let html = '';
        comments.forEach(comment => {
            const isAdmin = comment.commenter_type === 'admin';
            html += `
                <div class="comment-item mb-3 p-3 border rounded ${isAdmin ? 'bg-light' : ''}">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <div>
                            <strong>${escapeHtml(comment.commenter_name)}</strong>
                            ${isAdmin ? '<span class="badge bg-primary ms-1">Admin</span>' : ''}
                            <span class="badge bg-secondary ms-1">${comment.response_type.replace('_', ' ')}</span>
                        </div>
                        <small class="text-muted">${comment.formatted_time}</small>
                    </div>
                    <p class="mb-0">${escapeHtml(comment.comment).replace(/\\n/g, '<br>')}</p>
                </div>
            `;
        });

        return html;
    }

    // Global variable to store current request ID for comments
    let currentCommentsRequestId = null;

    function viewRequestComments(requestId) {
        currentCommentsRequestId = requestId;
        const modal = new bootstrap.Modal(document.getElementById('commentsViewModal'));
        const content = document.getElementById('commentsViewContent');

        // Show loading spinner
        content.innerHTML = `
            <div class="text-center">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading comments...</span>
                </div>
            </div>
        `;

        modal.show();

        // Fetch comments
        fetch(`ajax/get_request_comments.php?request_id=${requestId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayCommentsInModal(data.comments, requestId);
                } else {
                    content.innerHTML = `<div class="alert alert-danger">Error loading comments: ${data.error}</div>`;
                }
            })
            .catch(error => {
                console.error('Error fetching comments:', error);
                content.innerHTML = `<div class="alert alert-danger">Error loading comments. Please try again.</div>`;
            });
    }

    function displayCommentsInModal(comments, requestId) {
        const content = document.getElementById('commentsViewContent');

        if (comments.length === 0) {
            content.innerHTML = `
                <div class="text-center py-4">
                    <i class="bi bi-chat-dots display-4 text-muted"></i>
                    <h6 class="mt-3 text-muted">No comments yet</h6>
                    <p class="text-muted">Be the first to share your thoughts and support!</p>
                </div>
            `;
            return;
        }

        let html = `<div class="comments-container">`;

        comments.forEach(comment => {
            const isAdmin = comment.commenter_type === 'admin';
            const responseTypeDisplay = comment.response_type.replace('_', ' ');
            const responseTypeColors = {
                'support': 'success',
                'prayer': 'info',
                'guidance': 'primary',
                'offer_help': 'warning'
            };
            const badgeColor = responseTypeColors[comment.response_type] || 'secondary';

            html += `
                <div class="comment-item mb-3 p-3 border rounded ${isAdmin ? 'bg-light border-primary' : 'bg-white'}">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <div class="d-flex align-items-center gap-2">
                            <div class="d-flex align-items-center">
                                <i class="bi ${isAdmin ? 'bi-person-badge' : 'bi-person-circle'} me-2 ${isAdmin ? 'text-primary' : 'text-secondary'}"></i>
                                <strong class="${isAdmin ? 'text-primary' : ''}">${escapeHtml(comment.commenter_name)}</strong>
                            </div>
                            ${isAdmin ? '<span class="badge bg-primary ms-2">Admin</span>' : '<span class="badge bg-info ms-2">Member</span>'}
                            <span class="badge bg-${badgeColor} ms-1">${responseTypeDisplay}</span>
                        </div>
                        <small class="text-muted">${comment.formatted_time}</small>
                    </div>
                    <div class="comment-content">
                        <p class="mb-0">${escapeHtml(comment.comment).replace(/\\n/g, '<br>')}</p>
                    </div>
                </div>
            `;
        });

        html += `</div>`;
        content.innerHTML = html;
    }

    function showCommentFormFromView() {
        if (currentCommentsRequestId) {
            // Close the comments view modal
            const commentsModal = bootstrap.Modal.getInstance(document.getElementById('commentsViewModal'));
            if (commentsModal) {
                commentsModal.hide();
            }

            // Show the comment form modal
            showCommentForm(currentCommentsRequestId);
        }
    }

    function showCommentForm(requestId) {
        document.getElementById('commentRequestId').value = requestId;
        document.getElementById('commentText').value = '';
        document.getElementById('responseType').value = 'support';

        const modal = new bootstrap.Modal(document.getElementById('commentModal'));
        modal.show();
    }

    function submitComment(button) {
        const requestId = document.getElementById('commentRequestId').value;
        const comment = document.getElementById('commentText').value.trim();
        const responseType = document.getElementById('responseType').value;

        if (!comment) {
            alert('Please enter a comment.');
            return;
        }

        // Show loading state
        const submitBtn = button;
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> Posting...';
        submitBtn.disabled = true;

        fetch('ajax/add_request_comment.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                request_id: requestId,
                comment: comment,
                response_type: responseType
            })
        })
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);
            return response.text().then(text => {
                console.log('Raw response:', text);
                try {
                    return JSON.parse(text);
                } catch (e) {
                    console.error('JSON parse error:', e);
                    throw new Error('Invalid JSON response: ' + text);
                }
            });
        })
        .then(data => {
            console.log('Parsed data:', data);
            if (data.success) {
                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('commentModal'));
                if (modal) {
                    modal.hide();
                }

                // Clear the comment form
                document.getElementById('commentText').value = '';

                // Update comment count in the card
                const requestCard = document.querySelector(`#request-${requestId} .response-count`);
                if (requestCard) {
                    const countMatch = requestCard.textContent.match(/\d+/);
                    const currentCount = countMatch ? parseInt(countMatch[0]) : 0;
                    const newCount = data.updated ? currentCount : currentCount + 1;
                    requestCard.innerHTML = `<i class="bi bi-chat-dots"></i> ${newCount}`;
                }

                // Show success message
                const message = data.updated ? 'Comment updated successfully!' : 'Comment posted successfully!';
                showNotification(message, 'success');

                // Refresh request details if modal is open
                const detailsModal = document.getElementById('requestDetailsModal');
                if (detailsModal && detailsModal.classList.contains('show')) {
                    viewRequestDetails(requestId);
                }

                // Refresh comments view modal if it's open
                const commentsModal = document.getElementById('commentsViewModal');
                if (commentsModal && commentsModal.classList.contains('show')) {
                    viewRequestComments(requestId);
                }
            } else {
                console.error('Server error:', data.error);
                alert('Error posting comment: ' + (data.error || 'Unknown error occurred'));
            }
        })
        .catch(error => {
            console.error('Fetch error:', error);
            alert('Error posting comment: ' + error.message);
        })
        .finally(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    }

    function showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Add CSS for spinning animation
    const style = document.createElement('style');
    style.textContent = `
        .spin {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    `;
    document.head.appendChild(style);

    // Original functions
    function updateStatus(requestId, status) {
        let description = '';
        if (status === 'answered') {
            description = prompt('Please describe how this request was answered:');
            if (!description) return;
        }

        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="request_id" value="${requestId}">
            <input type="hidden" name="status" value="${status}">
            <input type="hidden" name="answered_description" value="${description}">
            <input type="hidden" name="update_status" value="1">
        `;
        document.body.appendChild(form);
        form.submit();
    }

    function showAdminResponses(requestId) {
        const modal = new bootstrap.Modal(document.getElementById('adminResponsesModal'));
        const content = document.getElementById('adminResponsesContent');

        // Show loading spinner
        content.innerHTML = `
            <div class="text-center">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        `;

        modal.show();

        // Fetch admin responses
        fetch('ajax/get_admin_responses.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'request_id=' + requestId
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = '';
                if (data.responses.length === 0) {
                    html = '<p class="text-muted text-center">No admin responses yet.</p>';
                } else {
                    data.responses.forEach(response => {
                        const responseTypeColors = {
                            'support': 'success',
                            'guidance': 'primary',
                            'prayer': 'info',
                            'referral': 'warning',
                            'follow_up': 'secondary',
                            'resolved': 'success'
                        };
                        const badgeColor = responseTypeColors[response.response_type] || 'secondary';

                        html += `
                            <div class="card mb-3">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <span class="badge bg-${badgeColor}">${response.response_type.replace('_', ' ').toUpperCase()}</span>
                                        <small class="text-muted">${new Date(response.created_at).toLocaleDateString()}</small>
                                    </div>
                                    <p class="card-text">${response.comment.replace(/\n/g, '<br>')}</p>
                                    <small class="text-muted">
                                        <i class="bi bi-person-badge"></i> Church Administration
                                    </small>
                                </div>
                            </div>
                        `;
                    });
                }
                content.innerHTML = html;
            } else {
                content.innerHTML = '<p class="text-danger">Error loading responses.</p>';
            }
        })
        .catch(error => {
            content.innerHTML = '<p class="text-danger">Error loading responses.</p>';
        });
    }

    // Delete request function
    function deleteRequest(requestId) {
        if (confirm('Are you sure you want to delete this request? This action cannot be undone.')) {
            fetch('ajax/delete_request.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    request_id: requestId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remove the request card from the page
                    const requestCard = document.querySelector(`[onclick*="updateStatus(${requestId}"]`).closest('.card');
                    if (requestCard) {
                        requestCard.remove();
                    }
                    showNotification('Request deleted successfully!', 'success');

                    // Refresh the page to update counts
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    alert('Error deleting request: ' + (data.error || 'Unknown error occurred'));
                }
            })
            .catch(error => {
                console.error('Delete error:', error);
                alert('Error deleting request. Please try again.');
            });
        }
    }

    // Sticky navigation scroll effect
    window.addEventListener('scroll', function() {
        const navbar = document.querySelector('.navbar');
        if (window.scrollY > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });
    </script>

    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>
</body>
</html>
