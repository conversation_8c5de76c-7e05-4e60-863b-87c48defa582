<?php
/**
 * User Profile Management
 *
 * Allows authenticated users to view and edit their profile information
 */

session_start();

// Include configuration and classes
require_once '../config.php';
require_once '../classes/SecurityManager.php';
require_once '../classes/UserAuthManager.php';

// Ensure all profile columns exist
try {
    // Check and add missing columns
    $columns_to_add = [
        'preferred_communication' => "ENUM('email', 'phone', 'sms', 'whatsapp') DEFAULT 'email'",
        'bio' => "TEXT NULL",
        'interests' => "TEXT NULL",
        'emergency_contact_name' => "VARCHAR(255) NULL",
        'emergency_contact_phone' => "VARCHAR(20) NULL",
        'emergency_contact_relationship' => "VARCHAR(100) NULL",
        'medical_conditions' => "TEXT NULL",
        'allergies' => "TEXT NULL",
        'anniversary_date' => "DATE NULL",
        'baptism_date' => "DATE NULL",
        'volunteer_interests' => "TEXT NULL",
        'availability_schedule' => "TEXT NULL",
        'social_media_links' => "JSON NULL",
        'privacy_settings' => "JSON NULL",
        'notification_settings' => "JSON NULL",
        'profile_photo_path' => "VARCHAR(500) NULL",
        'cover_photo_path' => "VARCHAR(500) NULL",
        'profile_visibility' => "ENUM('public', 'members', 'private') DEFAULT 'members'",
        'timezone' => "VARCHAR(50) DEFAULT 'America/New_York'",
        'language' => "VARCHAR(10) DEFAULT 'en'"
    ];

    foreach ($columns_to_add as $column => $definition) {
        $stmt = $pdo->query("SHOW COLUMNS FROM members LIKE '$column'");
        if ($stmt->rowCount() == 0) {
            $pdo->exec("ALTER TABLE members ADD COLUMN $column $definition");
        }
    }
} catch (PDOException $e) {
    // If there's an error, we'll handle it gracefully
    error_log("Error checking/adding profile columns: " . $e->getMessage());
}

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Set security headers
$security->setSecurityHeaders();

$error = '';
$success = '';

// Check for success message from redirect
if (isset($_GET['success']) && $_GET['success'] == '1') {
    $success = "Profile updated successfully!";
}

// Check if user is authenticated
if (!$userAuth->isAuthenticated()) {
    header("Location: login.php");
    exit();
}

$userId = $_SESSION['user_id'];
$userData = $userAuth->getUserById($userId);

if (!$userData) {
    session_destroy();
    header("Location: login.php");
    exit();
}

// Check if user must change password (redirect to change password page)
if ($userData['must_change_password']) {
    header("Location: change_password.php");
    exit();
}

// Process profile update form
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_profile'])) {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !$security->validateCSRFToken($_POST['csrf_token'])) {
        $error = "Invalid form submission. Please try again.";
    } else {
        // Get the current tab being submitted
        $currentTab = $_POST['current_tab'] ?? 'basic';

        // Get existing user data to preserve fields not being updated
        $existingData = $userAuth->getUserById($userId);

        $profileData = [
            'full_name' => trim($_POST['full_name'] ?? $existingData['full_name'] ?? ''),
            'first_name' => trim($_POST['first_name'] ?? $existingData['first_name'] ?? ''),
            'last_name' => trim($_POST['last_name'] ?? $existingData['last_name'] ?? ''),
            'phone_number' => trim($_POST['phone_number'] ?? $existingData['phone_number'] ?? ''),
            'birth_date' => trim($_POST['birth_date'] ?? $existingData['birth_date'] ?? ''),
            'gender' => trim($_POST['gender'] ?? $existingData['gender'] ?? ''),
            'home_address' => trim($_POST['home_address'] ?? $existingData['home_address'] ?? ''),
            'occupation' => trim($_POST['occupation'] ?? $existingData['occupation'] ?? ''),
            'bio' => trim($_POST['bio'] ?? $existingData['bio'] ?? ''),
            'interests' => trim($_POST['interests'] ?? $existingData['interests'] ?? ''),
            'emergency_contact_name' => trim($_POST['emergency_contact_name'] ?? $existingData['emergency_contact_name'] ?? ''),
            'emergency_contact_phone' => trim($_POST['emergency_contact_phone'] ?? $existingData['emergency_contact_phone'] ?? ''),
            'emergency_contact_relationship' => trim($_POST['emergency_contact_relationship'] ?? $existingData['emergency_contact_relationship'] ?? ''),
            'medical_conditions' => trim($_POST['medical_conditions'] ?? $existingData['medical_conditions'] ?? ''),
            'allergies' => trim($_POST['allergies'] ?? $existingData['allergies'] ?? ''),
            'preferred_communication' => trim($_POST['preferred_communication'] ?? $existingData['preferred_communication'] ?? ''),
            'anniversary_date' => trim($_POST['anniversary_date'] ?? $existingData['anniversary_date'] ?? '') ?: null,
            'baptism_date' => trim($_POST['baptism_date'] ?? $existingData['baptism_date'] ?? '') ?: null,
            'volunteer_interests' => trim($_POST['volunteer_interests'] ?? $existingData['volunteer_interests'] ?? ''),
            'availability_schedule' => trim($_POST['availability_schedule'] ?? $existingData['availability_schedule'] ?? ''),
        ];

        // Handle social media links for social tab
        if ($currentTab === 'social') {
            $socialLinks = [
                'facebook' => trim($_POST['facebook'] ?? ''),
                'twitter' => trim($_POST['twitter'] ?? ''),
                'instagram' => trim($_POST['instagram'] ?? ''),
                'linkedin' => trim($_POST['linkedin'] ?? ''),
                'website' => trim($_POST['website'] ?? ''),
            ];
            $profileData['social_media_links'] = json_encode($socialLinks);
        }

        // Tab-specific validation
        $validationError = false;

        if ($currentTab === 'basic') {
            // Only validate basic info fields when basic tab is being submitted
            if (empty($profileData['full_name'])) {
                $error = "Full name is required.";
                $validationError = true;
            } elseif (!empty($profileData['phone_number']) && !$security->validateInput($profileData['phone_number'], 'phone')) {
                $error = "Please enter a valid phone number.";
                $validationError = true;
            }
        } elseif ($currentTab === 'emergency') {
            // Validate emergency contact fields if any are provided
            if (!empty($profileData['emergency_contact_phone']) && !$security->validateInput($profileData['emergency_contact_phone'], 'phone')) {
                $error = "Please enter a valid emergency contact phone number.";
                $validationError = true;
            }
        }
        // Other tabs (extended, medical, social, privacy) don't have required field validation

        if (!$validationError) {
            // Handle profile image upload
            $imageUpdated = false;
            $currentImagePath = $userData['image_path'];
            
            // Check if user wants to remove current image
            if (isset($_POST['remove_image']) && $_POST['remove_image'] == '1') {
                // Delete old image if it exists and is not default
                if (!empty($currentImagePath) && $currentImagePath != 'assets/img/default-profile.jpg') {
                    $oldImagePath = '../' . $currentImagePath;
                    if (file_exists($oldImagePath)) {
                        unlink($oldImagePath);
                    }
                }
                $profileData['image_path'] = null;
                $imageUpdated = true;
            } elseif (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] == 0) {
                try {
                    $allowedTypes = ['jpg', 'jpeg', 'png', 'gif'];
                    $fileExtension = strtolower(pathinfo($_FILES['profile_image']['name'], PATHINFO_EXTENSION));
                    
                    if (!in_array($fileExtension, $allowedTypes)) {
                        throw new Exception('Invalid file type. Please upload JPG, PNG, or GIF files only.');
                    }
                    
                    // Check file size (max 5MB)
                    if ($_FILES['profile_image']['size'] > 5 * 1024 * 1024) {
                        throw new Exception('File size too large. Please upload an image smaller than 5MB.');
                    }
                    
                    $uploadDir = '../uploads/profiles/';

                    // Create upload directory if it doesn't exist
                    if (!is_dir($uploadDir)) {
                        if (!mkdir($uploadDir, 0755, true)) {
                            throw new Exception('Failed to create upload directory.');
                        }
                    }

                    // Check if directory is writable
                    if (!is_writable($uploadDir)) {
                        throw new Exception('Upload directory is not writable. Please check permissions.');
                    }

                    // Generate unique filename
                    $filename = uniqid() . '.' . $fileExtension;
                    $uploadPath = $uploadDir . $filename;
                    
                    if (move_uploaded_file($_FILES['profile_image']['tmp_name'], $uploadPath)) {
                        // Delete old image if it exists and is not default
                        if (!empty($currentImagePath) && $currentImagePath != 'assets/img/default-profile.jpg') {
                            $oldImagePath = '../' . $currentImagePath;
                            if (file_exists($oldImagePath)) {
                                unlink($oldImagePath);
                            }
                        }
                        
                        $profileData['image_path'] = 'uploads/profiles/' . $filename;
                        $imageUpdated = true;
                    } else {
                        throw new Exception('Failed to upload image.');
                    }
                } catch (Exception $e) {
                    $error = $e->getMessage();
                }
            }
            
            if (empty($error)) {
                // Handle social media links
                if (isset($_POST['update_social_media'])) {
                    $social_links = json_encode([
                        'facebook' => trim($_POST['facebook'] ?? ''),
                        'twitter' => trim($_POST['twitter'] ?? ''),
                        'instagram' => trim($_POST['instagram'] ?? ''),
                        'linkedin' => trim($_POST['linkedin'] ?? ''),
                        'website' => trim($_POST['website'] ?? '')
                    ]);
                    $profileData['social_media_links'] = $social_links;
                }

                // Handle privacy settings
                if (isset($_POST['update_privacy'])) {
                    $privacy_settings = json_encode([
                        'profile_visibility' => $_POST['profile_visibility'] ?? 'members',
                        'contact_visibility' => $_POST['contact_visibility'] ?? 'family',
                        'birthday_visibility' => $_POST['birthday_visibility'] ?? 'members',
                        'family_visibility' => $_POST['family_visibility'] ?? 'family',
                        'activity_visibility' => $_POST['activity_visibility'] ?? 'members'
                    ]);
                    $profileData['privacy_settings'] = $privacy_settings;
                }

                // Handle notification preferences
                if (isset($_POST['update_notifications'])) {
                    $notification_settings = json_encode([
                        'email_events' => isset($_POST['email_events']),
                        'email_reminders' => isset($_POST['email_reminders']),
                        'email_birthdays' => isset($_POST['email_birthdays']),
                        'email_prayers' => isset($_POST['email_prayers']),
                        'sms_events' => isset($_POST['sms_events']),
                        'sms_reminders' => isset($_POST['sms_reminders']),
                        'sms_urgent' => isset($_POST['sms_urgent'])
                    ]);
                    $profileData['notification_settings'] = $notification_settings;
                }

                // Update profile
                $result = $userAuth->updateProfile($userId, $profileData);

                if ($result['success']) {
                    $success = "Profile updated successfully!";

                    // Update session data
                    $_SESSION['user_name'] = $profileData['full_name'];

                    // Refresh user data
                    $userData = $userAuth->getUserById($userId);

                    // Redirect to maintain the active tab
                    header("Location: profile.php?tab=" . $currentTab . "&success=1");
                    exit();
                } else {
                    $error = $result['message'];
                }
            }
        }
    }
}

// Parse JSON fields
$social_links = json_decode($userData['social_media_links'] ?? '{}', true) ?: [];
$privacy_settings = json_decode($userData['privacy_settings'] ?? '{}', true) ?: [];
$notification_settings = json_decode($userData['notification_settings'] ?? '{}', true) ?: [];

// Get site settings for branding
$sitename = get_organization_name() . ' - Profile';
$stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'site_name'");
$stmt->execute();
$siteNameResult = $stmt->fetch();
if ($siteNameResult) {
    $sitename = $siteNameResult['setting_value'];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Profile - <?php echo htmlspecialchars($sitename); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Custom Theme CSS from Appearance Settings -->
    <?php include 'includes/theme_css.php'; ?>

    <style>
        body {
            background-color: #f8f9fa;
            font-family: var(--font-family, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif);
        }

        .navbar {
            background: var(--bs-primary, #fd7e14) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 700;
            color: white !important;
        }

        .navbar-nav .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .navbar-nav .nav-link:hover {
            color: white !important;
        }
        
        .profile-container {
            margin-top: 2rem;
        }
        
        .profile-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border: none;
        }
        
        .profile-header {
            background: linear-gradient(135deg, var(--bs-primary, #fd7e14) 0%, var(--bs-secondary, #6c757d) 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .profile-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid rgba(255,255,255,0.3);
            margin-bottom: 1rem;
        }
        
        .profile-avatar-placeholder {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: rgba(255,255,255,0.2);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            border: 4px solid rgba(255,255,255,0.3);
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-primary {
            background: var(--bs-primary, #fd7e14);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            opacity: 0.9;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(253, 126, 20, 0.3);
        }
        
        .btn-secondary {
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
        }
        
        .btn-outline-danger {
            border-radius: 10px;
            padding: 0.5rem 1rem;
            font-weight: 500;
        }
        
        .alert {
            border-radius: 12px;
            border: none;
            margin-bottom: 1.5rem;
        }
        
        .alert-danger {
            background-color: #fee;
            color: #c33;
        }
        
        .alert-success {
            background-color: #efe;
            color: #363;
        }
        
        .form-label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }
        
        .required-field::after {
            content: " *";
            color: #dc3545;
        }
        
        .image-upload-section {
            background-color: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .image-upload-section:hover {
            border-color: #667eea;
            background-color: #f0f4ff;
        }
        
        .image-preview {
            max-width: 200px;
            max-height: 200px;
            border-radius: 10px;
            margin-top: 1rem;
        }
        
        .profile-stats {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .stat-item {
            text-align: center;
            padding: 0.5rem;
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #667eea;
        }
        
        .stat-label {
            font-size: 0.875rem;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <div class="container profile-container">
        <!-- Profile Header -->
        <div class="profile-header">
            <?php if (!empty($userData['image_path'])): ?>
                <img src="../<?php echo htmlspecialchars($userData['image_path']); ?>?v=<?php echo time(); ?>" alt="Profile" class="profile-avatar">
            <?php else: ?>
                <div class="profile-avatar-placeholder">
                    <i class="bi bi-person-fill" style="font-size: 3rem;"></i>
                </div>
            <?php endif; ?>
            <h2><?php echo htmlspecialchars($userData['full_name'] ?? 'Member'); ?></h2>
            <p class="mb-0"><?php echo htmlspecialchars($userData['email'] ?? 'No email provided'); ?></p>
            <?php if (!empty($userData['occupation'])): ?>
                <p class="mb-0 opacity-75"><?php echo htmlspecialchars($userData['occupation']); ?></p>
            <?php endif; ?>
        </div>

        <?php if (!empty($error)): ?>
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($success)): ?>
            <div class="alert alert-success">
                <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($success); ?>
            </div>
        <?php endif; ?>

        <div class="row">
            <!-- Profile Form -->
            <div class="col-lg-8">
                <div class="profile-card">
                    <h4 class="mb-4"><i class="bi bi-person-gear"></i> Edit Profile Information</h4>

                    <!-- Profile Tabs -->
                    <ul class="nav nav-tabs mb-4" id="profileTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic" type="button" role="tab">
                                <i class="bi bi-person"></i> Basic Info
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="extended-tab" data-bs-toggle="tab" data-bs-target="#extended" type="button" role="tab">
                                <i class="bi bi-info-circle"></i> Extended Info
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="emergency-tab" data-bs-toggle="tab" data-bs-target="#emergency" type="button" role="tab">
                                <i class="bi bi-shield-exclamation"></i> Emergency & Medical
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="social-tab" data-bs-toggle="tab" data-bs-target="#social" type="button" role="tab">
                                <i class="bi bi-share"></i> Social Media
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="privacy-tab" data-bs-toggle="tab" data-bs-target="#privacy" type="button" role="tab">
                                <i class="bi bi-shield-lock"></i> Privacy
                            </button>
                        </li>
                    </ul>

                    <div class="tab-content" id="profileTabsContent">
                        <!-- Basic Information Tab -->
                        <div class="tab-pane fade show active" id="basic" role="tabpanel">
                            <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" enctype="multipart/form-data">
                        <?php echo $security->generateCSRFInput(); ?>
                        <input type="hidden" name="current_tab" value="basic">
                        
                        <!-- Profile Image Section -->
                        <div class="mb-4">
                            <label class="form-label">Profile Photo</label>
                            <div class="image-upload-section">
                                <?php if (!empty($userData['image_path'])): ?>
                                    <div class="current-image mb-3">
                                        <img src="../<?php echo htmlspecialchars($userData['image_path']); ?>" alt="Current Profile" class="image-preview">
                                        <div class="mt-2">
                                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeCurrentImage()">
                                                <i class="bi bi-trash"></i> Remove Current Photo
                                            </button>
                                        </div>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="upload-area">
                                    <i class="bi bi-cloud-upload" style="font-size: 2rem; color: #667eea;"></i>
                                    <p class="mb-2">Upload a new profile photo</p>
                                    <input type="file" class="form-control" id="profile_image" name="profile_image" accept="image/*" onchange="previewImage(this)">
                                    <small class="text-muted">JPG, PNG, or GIF. Max size: 5MB</small>
                                </div>
                                
                                <div id="image_preview_container" class="d-none">
                                    <img id="image_preview" class="image-preview">
                                </div>
                                
                                <input type="hidden" id="remove_image" name="remove_image" value="0">
                            </div>
                        </div>
                        
                        <!-- Personal Information -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="full_name" class="form-label required-field">Full Name</label>
                                    <input type="text" class="form-control" id="full_name" name="full_name"
                                           value="<?php echo htmlspecialchars($userData['full_name'] ?? ''); ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email Address</label>
                                    <input type="email" class="form-control" id="email" name="email"
                                           value="<?php echo htmlspecialchars($userData['email'] ?? ''); ?>" readonly>
                                    <small class="text-muted">Email cannot be changed. Contact an administrator if needed.</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="first_name" class="form-label">First Name</label>
                                    <input type="text" class="form-control" id="first_name" name="first_name" 
                                           value="<?php echo htmlspecialchars($userData['first_name'] ?? ''); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="last_name" class="form-label">Last Name</label>
                                    <input type="text" class="form-control" id="last_name" name="last_name" 
                                           value="<?php echo htmlspecialchars($userData['last_name'] ?? ''); ?>">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone_number" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control" id="phone_number" name="phone_number" 
                                           value="<?php echo htmlspecialchars($userData['phone_number'] ?? ''); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="birth_date" class="form-label">Birth Date</label>
                                    <input type="date" class="form-control" id="birth_date" name="birth_date"
                                           value="<?php echo htmlspecialchars($userData['birth_date'] ?? ''); ?>">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="gender" class="form-label">Gender</label>
                                    <select class="form-control" id="gender" name="gender">
                                        <option value="">Please select...</option>
                                        <option value="Male" <?php echo ($userData['gender'] === 'Male') ? 'selected' : ''; ?>>Male</option>
                                        <option value="Female" <?php echo ($userData['gender'] === 'Female') ? 'selected' : ''; ?>>Female</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="occupation" class="form-label">Occupation</label>
                            <input type="text" class="form-control" id="occupation" name="occupation" 
                                   value="<?php echo htmlspecialchars($userData['occupation'] ?? ''); ?>">
                        </div>
                        
                        <div class="mb-3">
                            <label for="home_address" class="form-label">Home Address</label>
                            <textarea class="form-control" id="home_address" name="home_address" rows="3"><?php echo htmlspecialchars($userData['home_address'] ?? ''); ?></textarea>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" name="update_profile" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> Update Basic Information
                            </button>
                            <a href="dashboard.php" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> Back to Dashboard
                            </a>
                        </div>
                    </form>
                        </div>

                        <!-- Extended Information Tab -->
                        <div class="tab-pane fade" id="extended" role="tabpanel">
                            <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
                                <?php echo $security->generateCSRFInput(); ?>
                                <input type="hidden" name="current_tab" value="extended">

                                <div class="mb-3">
                                    <label for="bio" class="form-label">Bio</label>
                                    <textarea class="form-control" id="bio" name="bio" rows="4"
                                              placeholder="Tell us about yourself..."><?php echo htmlspecialchars($userData['bio'] ?? ''); ?></textarea>
                                </div>

                                <div class="mb-3">
                                    <label for="interests" class="form-label">Interests & Hobbies</label>
                                    <textarea class="form-control" id="interests" name="interests" rows="3"
                                              placeholder="What are your interests and hobbies?"><?php echo htmlspecialchars($userData['interests'] ?? ''); ?></textarea>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="anniversary_date" class="form-label">Anniversary Date</label>
                                            <input type="date" class="form-control" id="anniversary_date" name="anniversary_date"
                                                   value="<?php echo htmlspecialchars($userData['anniversary_date'] ?? ''); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="baptism_date" class="form-label">Baptism Date</label>
                                            <input type="date" class="form-control" id="baptism_date" name="baptism_date"
                                                   value="<?php echo htmlspecialchars($userData['baptism_date'] ?? ''); ?>">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="volunteer_interests" class="form-label">Volunteer Interests</label>
                                    <textarea class="form-control" id="volunteer_interests" name="volunteer_interests" rows="3"
                                              placeholder="What volunteer activities interest you?"><?php echo htmlspecialchars($userData['volunteer_interests'] ?? ''); ?></textarea>
                                </div>

                                <div class="mb-3">
                                    <label for="availability_schedule" class="form-label">Availability Schedule</label>
                                    <textarea class="form-control" id="availability_schedule" name="availability_schedule" rows="3"
                                              placeholder="When are you typically available for activities?"><?php echo htmlspecialchars($userData['availability_schedule'] ?? ''); ?></textarea>
                                </div>

                                <div class="mb-3">
                                    <label for="preferred_communication" class="form-label">Preferred Communication Method</label>
                                    <select class="form-select" id="preferred_communication" name="preferred_communication">
                                        <option value="email" <?php echo (($userData['preferred_communication'] ?? '') === 'email') ? 'selected' : ''; ?>>Email</option>
                                        <option value="phone" <?php echo (($userData['preferred_communication'] ?? '') === 'phone') ? 'selected' : ''; ?>>Phone</option>
                                        <option value="sms" <?php echo (($userData['preferred_communication'] ?? '') === 'sms') ? 'selected' : ''; ?>>SMS/Text</option>
                                        <option value="whatsapp" <?php echo (($userData['preferred_communication'] ?? '') === 'whatsapp') ? 'selected' : ''; ?>>WhatsApp</option>
                                    </select>
                                </div>

                                <button type="submit" name="update_profile" class="btn btn-primary">
                                    <i class="bi bi-check-circle"></i> Update Extended Information
                                </button>
                            </form>
                        </div>

                        <!-- Emergency & Medical Tab -->
                        <div class="tab-pane fade" id="emergency" role="tabpanel">
                            <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
                                <?php echo $security->generateCSRFInput(); ?>
                                <input type="hidden" name="current_tab" value="emergency">

                                <h5 class="mb-3">Emergency Contact Information</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="emergency_contact_name" class="form-label">Emergency Contact Name</label>
                                            <input type="text" class="form-control" id="emergency_contact_name" name="emergency_contact_name"
                                                   value="<?php echo htmlspecialchars($userData['emergency_contact_name'] ?? ''); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="emergency_contact_phone" class="form-label">Emergency Contact Phone</label>
                                            <input type="tel" class="form-control" id="emergency_contact_phone" name="emergency_contact_phone"
                                                   value="<?php echo htmlspecialchars($userData['emergency_contact_phone'] ?? ''); ?>">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="emergency_contact_relationship" class="form-label">Relationship to Emergency Contact</label>
                                    <input type="text" class="form-control" id="emergency_contact_relationship" name="emergency_contact_relationship"
                                           value="<?php echo htmlspecialchars($userData['emergency_contact_relationship'] ?? ''); ?>"
                                           placeholder="e.g., spouse, parent, sibling, friend">
                                </div>

                                <h5 class="mb-3 mt-4">Medical Information</h5>
                                <div class="mb-3">
                                    <label for="medical_conditions" class="form-label">Medical Conditions</label>
                                    <textarea class="form-control" id="medical_conditions" name="medical_conditions" rows="3"
                                              placeholder="Any medical conditions we should be aware of..."><?php echo htmlspecialchars($userData['medical_conditions'] ?? ''); ?></textarea>
                                </div>

                                <div class="mb-3">
                                    <label for="allergies" class="form-label">Allergies</label>
                                    <textarea class="form-control" id="allergies" name="allergies" rows="3"
                                              placeholder="Any allergies or dietary restrictions..."><?php echo htmlspecialchars($userData['allergies'] ?? ''); ?></textarea>
                                </div>

                                <button type="submit" name="update_profile" class="btn btn-primary">
                                    <i class="bi bi-check-circle"></i> Update Emergency & Medical Information
                                </button>
                            </form>
                        </div>

                        <!-- Social Media Tab -->
                        <div class="tab-pane fade" id="social" role="tabpanel">
                            <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
                                <?php echo $security->generateCSRFInput(); ?>
                                <input type="hidden" name="current_tab" value="social">

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="facebook" class="form-label"><i class="bi bi-facebook text-primary"></i> Facebook</label>
                                            <input type="url" class="form-control" id="facebook" name="facebook"
                                                   value="<?php echo htmlspecialchars($social_links['facebook'] ?? ''); ?>"
                                                   placeholder="https://facebook.com/yourprofile">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="twitter" class="form-label"><i class="bi bi-twitter text-info"></i> Twitter</label>
                                            <input type="url" class="form-control" id="twitter" name="twitter"
                                                   value="<?php echo htmlspecialchars($social_links['twitter'] ?? ''); ?>"
                                                   placeholder="https://twitter.com/yourusername">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="instagram" class="form-label"><i class="bi bi-instagram text-danger"></i> Instagram</label>
                                            <input type="url" class="form-control" id="instagram" name="instagram"
                                                   value="<?php echo htmlspecialchars($social_links['instagram'] ?? ''); ?>"
                                                   placeholder="https://instagram.com/yourusername">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="linkedin" class="form-label"><i class="bi bi-linkedin text-primary"></i> LinkedIn</label>
                                            <input type="url" class="form-control" id="linkedin" name="linkedin"
                                                   value="<?php echo htmlspecialchars($social_links['linkedin'] ?? ''); ?>"
                                                   placeholder="https://linkedin.com/in/yourprofile">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="website" class="form-label"><i class="bi bi-globe"></i> Personal Website</label>
                                    <input type="url" class="form-control" id="website" name="website"
                                           value="<?php echo htmlspecialchars($social_links['website'] ?? ''); ?>"
                                           placeholder="https://yourwebsite.com">
                                </div>

                                <button type="submit" name="update_social_media" class="btn btn-primary">
                                    <i class="bi bi-check-circle"></i> Update Social Media Links
                                </button>
                            </form>
                        </div>

                        <!-- Privacy Tab -->
                        <div class="tab-pane fade" id="privacy" role="tabpanel">
                            <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
                                <?php echo $security->generateCSRFInput(); ?>
                                <input type="hidden" name="current_tab" value="privacy">

                                <h5 class="mb-3">Privacy Settings</h5>
                                <p class="text-muted">Control who can see your information and activities.</p>

                                <div class="mb-3">
                                    <label for="profile_visibility" class="form-label">Profile Visibility</label>
                                    <select class="form-select" id="profile_visibility" name="profile_visibility">
                                        <option value="public" <?php echo ($privacy_settings['profile_visibility'] ?? 'members') === 'public' ? 'selected' : ''; ?>>Public (Everyone)</option>
                                        <option value="members" <?php echo ($privacy_settings['profile_visibility'] ?? 'members') === 'members' ? 'selected' : ''; ?>>Members Only</option>
                                        <option value="family" <?php echo ($privacy_settings['profile_visibility'] ?? 'members') === 'family' ? 'selected' : ''; ?>>Family Only</option>
                                        <option value="private" <?php echo ($privacy_settings['profile_visibility'] ?? 'members') === 'private' ? 'selected' : ''; ?>>Private</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="contact_visibility" class="form-label">Contact Information Visibility</label>
                                    <select class="form-select" id="contact_visibility" name="contact_visibility">
                                        <option value="public" <?php echo ($privacy_settings['contact_visibility'] ?? 'family') === 'public' ? 'selected' : ''; ?>>Public (Everyone)</option>
                                        <option value="members" <?php echo ($privacy_settings['contact_visibility'] ?? 'family') === 'members' ? 'selected' : ''; ?>>Members Only</option>
                                        <option value="family" <?php echo ($privacy_settings['contact_visibility'] ?? 'family') === 'family' ? 'selected' : ''; ?>>Family Only</option>
                                        <option value="private" <?php echo ($privacy_settings['contact_visibility'] ?? 'family') === 'private' ? 'selected' : ''; ?>>Private</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="birthday_visibility" class="form-label">Birthday Visibility</label>
                                    <select class="form-select" id="birthday_visibility" name="birthday_visibility">
                                        <option value="public" <?php echo ($privacy_settings['birthday_visibility'] ?? 'members') === 'public' ? 'selected' : ''; ?>>Public (Everyone)</option>
                                        <option value="members" <?php echo ($privacy_settings['birthday_visibility'] ?? 'members') === 'members' ? 'selected' : ''; ?>>Members Only</option>
                                        <option value="family" <?php echo ($privacy_settings['birthday_visibility'] ?? 'members') === 'family' ? 'selected' : ''; ?>>Family Only</option>
                                        <option value="private" <?php echo ($privacy_settings['birthday_visibility'] ?? 'members') === 'private' ? 'selected' : ''; ?>>Private</option>
                                    </select>
                                </div>

                                <button type="submit" name="update_privacy" class="btn btn-primary">
                                    <i class="bi bi-shield-check"></i> Update Privacy Settings
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Profile Stats & Info -->
            <div class="col-lg-4">
                <div class="profile-card">
                    <h5 class="mb-3"><i class="bi bi-info-circle"></i> Account Information</h5>
                    
                    <div class="profile-stats">
                        <div class="row">
                            <div class="col-6">
                                <div class="stat-item">
                                    <div class="stat-number">0</div>
                                    <div class="stat-label">Events</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="stat-item">
                                    <div class="stat-number">0</div>
                                    <div class="stat-label">Messages</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Member Since:</strong><br>
                        <span class="text-muted"><?php echo date('F j, Y', strtotime($userData['created_at'])); ?></span>
                    </div>
                    
                    <?php if (!empty($userData['last_login_at'])): ?>
                        <div class="mb-3">
                            <strong>Last Login:</strong><br>
                            <span class="text-muted"><?php echo date('M j, Y g:i A', strtotime($userData['last_login_at'])); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <div class="mb-3">
                        <strong>Account Status:</strong><br>
                        <span class="badge bg-success">Active</span>
                    </div>
                </div>

                <!-- Skills Section -->
                <div class="profile-card">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0"><i class="bi bi-tools"></i> My Skills</h5>
                        <a href="skills.php" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-plus-circle"></i> Manage Skills
                        </a>
                    </div>

                    <?php
                    // Get user's skills for profile display
                    try {
                        $stmt = $pdo->prepare("
                            SELECT ms.*, sc.skill_name, sc.skill_category
                            FROM member_skills ms
                            JOIN skills_catalog sc ON ms.skill_id = sc.id
                            WHERE ms.member_id = ?
                            ORDER BY sc.skill_category, sc.skill_name
                            LIMIT 6
                        ");
                        $stmt->execute([$userId]);
                        $profileSkills = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    } catch (PDOException $e) {
                        $profileSkills = [];
                    }
                    ?>

                    <?php if (empty($profileSkills)): ?>
                        <div class="text-center py-3">
                            <i class="bi bi-tools text-muted" style="font-size: 2rem;"></i>
                            <p class="text-muted mt-2 mb-2">No skills added yet</p>
                            <a href="skills.php" class="btn btn-sm btn-primary">
                                <i class="bi bi-plus-circle"></i> Add Your First Skill
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="skills-display">
                            <?php foreach ($profileSkills as $skill): ?>
                                <div class="skill-item mb-2 p-2 border rounded">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong><?php echo htmlspecialchars($skill['skill_name']); ?></strong>
                                            <?php if ($skill['verified_by']): ?>
                                                <span class="badge bg-success ms-1" style="font-size: 0.7rem;">
                                                    <i class="bi bi-patch-check"></i> Verified
                                                </span>
                                            <?php endif; ?>
                                            <br>
                                            <small class="text-muted"><?php echo htmlspecialchars($skill['skill_category']); ?></small>
                                        </div>
                                        <div>
                                            <span class="badge bg-<?php
                                                echo $skill['proficiency_level'] === 'expert' ? 'warning' :
                                                    ($skill['proficiency_level'] === 'advanced' ? 'success' :
                                                    ($skill['proficiency_level'] === 'intermediate' ? 'info' : 'secondary'));
                                            ?>">
                                                <?php echo ucfirst($skill['proficiency_level']); ?>
                                            </span>
                                        </div>
                                    </div>

                                    <?php if ($skill['willing_to_teach'] || $skill['willing_to_volunteer']): ?>
                                        <div class="mt-1">
                                            <?php if ($skill['willing_to_teach']): ?>
                                                <span class="badge bg-primary me-1" style="font-size: 0.7rem;">
                                                    <i class="bi bi-mortarboard"></i> Teaching
                                                </span>
                                            <?php endif; ?>
                                            <?php if ($skill['willing_to_volunteer']): ?>
                                                <span class="badge bg-warning text-dark" style="font-size: 0.7rem;">
                                                    <i class="bi bi-hand-thumbs-up"></i> Volunteering
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>

                            <?php
                            // Check if there are more skills
                            $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM member_skills WHERE member_id = ?");
                            $stmt->execute([$userId]);
                            $totalSkills = $stmt->fetch()['total'];

                            if ($totalSkills > 6):
                            ?>
                                <div class="text-center mt-2">
                                    <a href="skills.php" class="btn btn-sm btn-outline-primary">
                                        View All <?php echo $totalSkills; ?> Skills <i class="bi bi-arrow-right"></i>
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="profile-card">
                    <h5 class="mb-3"><i class="bi bi-gear"></i> Quick Actions</h5>
                    <div class="d-grid gap-2">
                        <a href="change_password.php" class="btn btn-outline-primary">
                            <i class="bi bi-shield-lock"></i> Change Password
                        </a>
                        <a href="settings.php" class="btn btn-outline-primary">
                            <i class="bi bi-gear"></i> Account Settings
                        </a>
                        <a href="events.php" class="btn btn-outline-primary">
                            <i class="bi bi-calendar-event"></i> View Events
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function previewImage(input) {
            const previewContainer = document.getElementById('image_preview_container');
            const preview = document.getElementById('image_preview');
            const removeImageField = document.getElementById('remove_image');
            
            // Reset remove image flag when new image is selected
            removeImageField.value = '0';
            
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.src = e.target.result;
                    previewContainer.classList.remove('d-none');
                }
                reader.readAsDataURL(input.files[0]);
            } else {
                previewContainer.classList.add('d-none');
            }
        }
        
        function removeCurrentImage() {
            if (confirm('Are you sure you want to remove your current profile photo?')) {
                document.getElementById('remove_image').value = '1';
                
                // Hide current image
                const currentImage = document.querySelector('.current-image');
                if (currentImage) {
                    currentImage.style.display = 'none';
                }
                
                // Show confirmation
                const uploadArea = document.querySelector('.upload-area');
                uploadArea.innerHTML = '<div class="alert alert-info"><i class="bi bi-info-circle"></i> Current photo will be removed when you save changes.</div>' + uploadArea.innerHTML;
            }
        }
        
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert-dismissible');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);

        // Handle tab persistence after form submission
        document.addEventListener('DOMContentLoaded', function() {
            // Check if there's a submitted tab in the URL or session
            const urlParams = new URLSearchParams(window.location.search);
            const activeTab = urlParams.get('tab') || '<?php echo $_POST['current_tab'] ?? 'basic'; ?>';

            if (activeTab && activeTab !== 'basic') {
                // Activate the correct tab
                const tabElement = document.querySelector(`#profileTabs a[href="#${activeTab}"]`);
                const tabPane = document.querySelector(`#${activeTab}`);

                if (tabElement && tabPane) {
                    // Remove active classes from current tab
                    document.querySelector('#profileTabs .nav-link.active')?.classList.remove('active');
                    document.querySelector('.tab-pane.active')?.classList.remove('active', 'show');

                    // Add active classes to target tab
                    tabElement.classList.add('active');
                    tabPane.classList.add('active', 'show');
                }
            }

            // Update current_tab hidden field when tab is clicked
            document.querySelectorAll('#profileTabs .nav-link').forEach(function(tabLink) {
                tabLink.addEventListener('click', function(e) {
                    const targetTab = this.getAttribute('href').substring(1);

                    // Update all current_tab hidden fields
                    document.querySelectorAll('input[name="current_tab"]').forEach(function(input) {
                        input.value = targetTab;
                    });
                });
            });
        });
    </script>

    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>
</body>
</html>
