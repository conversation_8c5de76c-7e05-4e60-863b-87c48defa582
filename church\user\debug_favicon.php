<?php
/**
 * Debug Favicon Loading
 * 
 * Test script to debug favicon path resolution
 */

require_once '../config.php';

echo "<h2>Favicon Debug Information</h2>";

// Test favicon settings
$favicon_32_path = get_site_setting('favicon_32', '');
$favicon_16_path = get_site_setting('favicon_16', '');
$favicon_logo_path = get_site_setting('favicon_logo', '');
$favicon_path_old = get_site_setting('favicon_path', '');

echo "<h3>Favicon Settings from Database:</h3>";
echo "favicon_32: " . htmlspecialchars($favicon_32_path) . "<br>";
echo "favicon_16: " . htmlspecialchars($favicon_16_path) . "<br>";
echo "favicon_logo: " . htmlspecialchars($favicon_logo_path) . "<br>";
echo "favicon_path (old): " . htmlspecialchars($favicon_path_old) . "<br>";

echo "<h3>File Existence Tests:</h3>";

// Test file paths as used in theme_css.php
$base_path = dirname(dirname(__DIR__));
echo "Base path: " . $base_path . "<br><br>";

if ($favicon_32_path) {
    $full_path_32 = $base_path . '/' . $favicon_32_path;
    echo "favicon_32 full path: " . $full_path_32 . "<br>";
    echo "favicon_32 exists: " . (file_exists($full_path_32) ? 'YES' : 'NO') . "<br>";
    if (file_exists($full_path_32)) {
        echo "favicon_32 URL: " . get_base_url() . '/' . $favicon_32_path . "<br>";
    }
    echo "<br>";
}

if ($favicon_16_path) {
    $full_path_16 = $base_path . '/' . $favicon_16_path;
    echo "favicon_16 full path: " . $full_path_16 . "<br>";
    echo "favicon_16 exists: " . (file_exists($full_path_16) ? 'YES' : 'NO') . "<br>";
    if (file_exists($full_path_16)) {
        echo "favicon_16 URL: " . get_base_url() . '/' . $favicon_16_path . "<br>";
    }
    echo "<br>";
}

echo "<h3>Base URL Test:</h3>";
echo "get_base_url(): " . get_base_url() . "<br>";

echo "<h3>Current Working Directory:</h3>";
echo "getcwd(): " . getcwd() . "<br>";
echo "__DIR__: " . __DIR__ . "<br>";
echo "dirname(dirname(__DIR__)): " . dirname(dirname(__DIR__)) . "<br>";

echo "<h3>Favicon Logic Test (CORRECTED from theme_css.php):</h3>";
$favicon_path = '';

// First try to get favicon from the new logo management system
$favicon_32_path = get_site_setting('favicon_32', '');
if ($favicon_32_path) {
    // Check if file exists in church directory (correct location)
    $church_path = dirname(__DIR__) . '/' . $favicon_32_path;
    echo "Testing favicon_32 path: " . $church_path . "<br>";
    if (file_exists($church_path)) {
        $favicon_path = $favicon_32_path;
        echo "Using favicon_32: " . $favicon_path . "<br>";
    } else {
        echo "favicon_32 file not found at: " . $church_path . "<br>";
    }
}

if (!$favicon_path) {
    // Fallback to favicon_logo setting (used by admin)
    $favicon_logo_path = get_site_setting('favicon_logo', '');
    if ($favicon_logo_path) {
        // Check if file exists in church directory
        $church_path = dirname(__DIR__) . '/' . $favicon_logo_path;
        echo "Testing favicon_logo path: " . $church_path . "<br>";
        if (file_exists($church_path)) {
            $favicon_path = $favicon_logo_path;
            echo "Using favicon_logo: " . $favicon_path . "<br>";
        } else {
            echo "favicon_logo file not found at: " . $church_path . "<br>";
        }
    }
}

if (!$favicon_path) {
    // Final fallback to old favicon_path setting
    $favicon_path_old = get_site_setting('favicon_path', '');
    if ($favicon_path_old) {
        // Check if file exists in church directory
        $church_path = dirname(__DIR__) . '/' . $favicon_path_old;
        echo "Testing favicon_path (old) path: " . $church_path . "<br>";
        if (file_exists($church_path)) {
            $favicon_path = $favicon_path_old;
            echo "Using favicon_path (old): " . $favicon_path . "<br>";
        } else {
            echo "favicon_path (old) file not found at: " . $church_path . "<br>";
        }
    }
}

if ($favicon_path && file_exists(dirname(__DIR__) . '/' . $favicon_path)) {
    echo "<strong>Final favicon path: " . $favicon_path . "</strong><br>";
    echo "<strong>Final favicon URL: " . get_base_url() . '/' . $favicon_path . "</strong><br>";
    echo "<strong>File exists: YES</strong><br>";

    // Test if the URL is accessible
    $favicon_url = get_base_url() . '/' . $favicon_path;
    echo "<br><img src='" . $favicon_url . "' alt='Favicon Test' style='width: 32px; height: 32px;'> <- Favicon should appear here<br>";
} else {
    echo "<strong>No valid favicon found!</strong><br>";
}
?>
