<?php
// Debug script to check what member the admin is currently viewing
require_once 'config.php';

echo "<h2>Debug Current Admin View Issue</h2>";

// Check what member ID is being passed in the URL
$member_id = $_GET['id'] ?? 'NOT_SET';
echo "<h3>URL Parameter 'id': $member_id</h3>";

if ($member_id === 'NOT_SET') {
    echo "<p style='color: red;'>❌ No member ID provided in URL. Please access this page with ?id=MEMBER_ID</p>";
    echo "<h4>Available Members:</h4>";
    
    try {
        $stmt = $pdo->query("SELECT id, full_name, email FROM members ORDER BY id");
        $members = $stmt->fetchAll();
        echo "<ul>";
        foreach ($members as $member) {
            $testUrl = "debug_current_view.php?id=" . $member['id'];
            echo "<li><a href='$testUrl'>ID {$member['id']}: {$member['full_name']} ({$member['email']})</a></li>";
        }
        echo "</ul>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    }
} else {
    // Test with the provided member ID
    $member_id = intval($member_id);
    echo "<h3>Testing Member ID: $member_id</h3>";

    try {
        // This is the exact same query used in admin/view_member.php
        $stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
        $stmt->execute([$member_id]);
        $member = $stmt->fetch();

        if ($member) {
            echo "<h4>✅ Member Found: " . htmlspecialchars($member['full_name']) . "</h4>";
            
            // Test the exact conditions used in the admin view
            $extended_fields = [
                'bio' => $member['bio'] ?? null,
                'interests' => $member['interests'] ?? null,
                'emergency_contact_name' => $member['emergency_contact_name'] ?? null,
                'emergency_contact_phone' => $member['emergency_contact_phone'] ?? null,
                'emergency_contact_relationship' => $member['emergency_contact_relationship'] ?? null,
                'medical_conditions' => $member['medical_conditions'] ?? null,
                'allergies' => $member['allergies'] ?? null,
                'anniversary_date' => $member['anniversary_date'] ?? null,
                'baptism_date' => $member['baptism_date'] ?? null,
                'volunteer_interests' => $member['volunteer_interests'] ?? null,
                'availability_schedule' => $member['availability_schedule'] ?? null,
                'social_media_links' => $member['social_media_links'] ?? null
            ];
            
            echo "<h4>Field-by-Field Analysis:</h4>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>Field</th><th>Value</th><th>empty() Check</th><th>Admin Would Show</th></tr>";
            
            foreach ($extended_fields as $field => $value) {
                $isEmpty = empty($value);
                $adminDisplay = $isEmpty ? "Not provided" : "DATA SHOWN";
                $valueDisplay = $value ? substr(htmlspecialchars($value), 0, 50) . "..." : "NULL/EMPTY";
                
                $rowColor = $isEmpty ? "#ffebee" : "#e8f5e8";
                echo "<tr style='background-color: $rowColor;'>";
                echo "<td><strong>$field</strong></td>";
                echo "<td>$valueDisplay</td>";
                echo "<td>" . ($isEmpty ? "TRUE (empty)" : "FALSE (has data)") . "</td>";
                echo "<td><strong>$adminDisplay</strong></td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // Show the admin view URL for this member
            echo "<h4>Admin View URL for this member:</h4>";
            echo "<p><a href='admin/view_member.php?id=$member_id' target='_blank'>admin/view_member.php?id=$member_id</a></p>";
            
        } else {
            echo "<p style='color: red;'>❌ Member not found with ID: $member_id</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    }
}

echo "<h3>Quick Links:</h3>";
echo "<ul>";
echo "<li><a href='debug_current_view.php?id=51'>Test Godwin Bointa (ID 51)</a></li>";
echo "<li><a href='debug_current_view.php?id=29'>Test Ndivhuwo Machiba (ID 29)</a></li>";
echo "<li><a href='admin/members.php'>Go to Admin Members List</a></li>";
echo "</ul>";
?>
