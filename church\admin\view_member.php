<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Check if member ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: members.php");
    exit();
}

$member_id = intval($_GET['id']);

// Include the configuration file
require_once '../config.php';

// Include language system
require_once 'includes/language.php';

// Fix member term setting if it's empty (causing sidebar to show "s" instead of "Members")
function ensureMemberTermSetting() {
    global $pdo;

    try {
        // Check if member_term exists and has a value
        $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'member_term'");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$result || empty(trim($result['setting_value']))) {
            // Insert or update member_term with default value
            $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES ('member_term', 'Member')
                                  ON DUPLICATE KEY UPDATE setting_value = 'Member'");
            $stmt->execute();
            error_log("Fixed empty member_term setting - set to 'Member'");
        }
    } catch (PDOException $e) {
        error_log("Error fixing member_term setting: " . $e->getMessage());
    }
}

// Database connection - using the connection from config.php
$conn = $pdo;

// Fix member term setting
ensureMemberTermSetting();

// Ensure all profile columns exist for detailed view
try {
    $columns_to_add = [
        'bio' => "TEXT NULL",
        'interests' => "TEXT NULL",
        'emergency_contact_name' => "VARCHAR(255) NULL",
        'emergency_contact_phone' => "VARCHAR(20) NULL",
        'emergency_contact_relationship' => "VARCHAR(100) NULL",
        'medical_conditions' => "TEXT NULL",
        'allergies' => "TEXT NULL",
        'preferred_communication' => "ENUM('email', 'phone', 'sms', 'whatsapp') DEFAULT 'email'",
        'anniversary_date' => "DATE NULL",
        'baptism_date' => "DATE NULL",
        'volunteer_interests' => "TEXT NULL",
        'availability_schedule' => "TEXT NULL",
        'social_media_links' => "JSON NULL"
    ];

    foreach ($columns_to_add as $column => $definition) {
        $stmt = $pdo->query("SHOW COLUMNS FROM members LIKE '$column'");
        if ($stmt->rowCount() == 0) {
            $pdo->exec("ALTER TABLE members ADD COLUMN $column $definition");
        }
    }
} catch (PDOException $e) {
    error_log("Error checking/adding member profile columns: " . $e->getMessage());
}

// Get member details
$stmt = $conn->prepare("SELECT * FROM members WHERE id = ?");
$stmt->execute([$member_id]);
$member = $stmt->fetch();

if (!$member) {
    header("Location: members.php");
    exit();
}

// Pagination for Communication History
$comm_page = isset($_GET['comm_page']) ? max(1, (int)$_GET['comm_page']) : 1;
$comm_limit = 5; // Reduced from 10 to 5
$comm_offset = ($comm_page - 1) * $comm_limit;

// Get total count of communication logs
$stmt = $conn->prepare("SELECT COUNT(*) FROM email_logs WHERE member_id = ?");
$stmt->execute([$member_id]);
$total_logs = $stmt->fetchColumn();
$total_comm_pages = ceil($total_logs / $comm_limit);

// Get email logs for this member with pagination
$stmt = $conn->prepare("
    SELECT el.*, et.template_name,
           COALESCE(el.subject, et.subject, 'No subject') as display_subject
    FROM email_logs el
    LEFT JOIN email_templates et ON el.template_id = et.id
    WHERE el.member_id = ?
    ORDER BY el.sent_at DESC
    LIMIT ? OFFSET ?
");
$stmt->execute([$member_id, $comm_limit, $comm_offset]);
$logs = $stmt->fetchAll();

// Get email tracking stats for this member
$stmt = $conn->prepare("
    SELECT COUNT(*) as total_sent, 
           COUNT(CASE WHEN opened_at IS NOT NULL THEN 1 END) as total_opened,
           MAX(opened_at) as last_opened,
           AVG(opened_count) as avg_opens
    FROM email_tracking
    WHERE member_id = ?
");
$stmt->execute([$member_id]);
$emailStats = $stmt->fetch();

// Get upcoming birthday info
$birthDate = !empty($member['birth_date']) ? new DateTime($member['birth_date']) : null;
$upcomingBirthday = null;
$daysUntilBirthday = null;

if ($birthDate) {
    // Use server date, not client date for consistency
    $today = new DateTime(date('Y-m-d')); // Today at midnight for correct day calculation
    
    // Get birthday this year
    $currentYearBirthday = new DateTime($today->format('Y') . '-' . $birthDate->format('m') . '-' . $birthDate->format('d'));
    
    // If already passed, get next year's birthday
    if ($currentYearBirthday < $today) {
        $currentYearBirthday->modify('+1 year');
    }
    
    $interval = $today->diff($currentYearBirthday);
    $daysUntilBirthday = $interval->days;
    $upcomingBirthday = $currentYearBirthday->format('F j, Y');
    
    // Log for debugging
    error_log("Today: " . $today->format('Y-m-d'));
    error_log("Birth Date: " . $birthDate->format('Y-m-d'));
    error_log("Current Year Birthday: " . $currentYearBirthday->format('Y-m-d'));
    error_log("Days Until Birthday: " . $daysUntilBirthday);
}

// Close the database connection
$conn = null;

// Set page title and header for header.php
$page_title = __('view_member');
$page_header = __('member_details');

// Extra CSS for this page
$extra_css = '
<style>
    .profile-image, .profile-image-placeholder {
        width: 180px;
        height: 180px;
        border-radius: 50%;
        object-fit: cover;
        border: 5px solid #f8f9fa;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        margin: 15px auto;
    }
    .member-info {
        list-style: none;
        padding-left: 0;
    }
    .member-info li {
        padding: 12px 0;
        border-bottom: 1px solid rgba(0,0,0,0.05);
    }
    .member-info li:last-child {
        border-bottom: none;
    }
    .card {
        border: 1px solid rgba(0,0,0,0.125);
        border-radius: 15px;
        box-shadow: 0 0 20px rgba(0,0,0,0.05);
        margin-bottom: 24px;
        overflow: hidden;
    }
    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid rgba(0,0,0,0.125);
        padding: 20px;
    }
    .card-header h5 {
        margin: 0;
        font-weight: 600;
        color: #2c3e50;
    }
    .card-body {
        padding: 24px;
    }
    .engagement-stats {
        background: #f8f9fa;
        border: 1px solid rgba(0,0,0,0.125);
        border-radius: 12px;
        padding: 20px;
        margin-top: 24px;
    }
    .stats-number {
        font-size: 28px;
        font-weight: 600;
        color: #0d6efd;
        margin-bottom: 8px;
    }
    .stats-label {
        font-size: 14px;
        color: #6c757d;
        font-weight: 500;
    }
    .birthday-alert {
        background: #fff3cd;
        border: 1px solid #ffeeba;
        border-radius: 10px;
        padding: 15px;
        margin: 15px 0;
    }
    .birthday-info {
        background: white;
        border: 1px solid rgba(0,0,0,0.125);
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    }
    .table {
        margin: 0;
    }
    .table th {
        font-weight: 600;
        color: #2c3e50;
        border-top: none;
        background-color: #f8f9fa;
    }
    .badge {
        padding: 8px 12px;
        border-radius: 6px;
        font-weight: 500;
    }
    .btn {
        padding: 8px 16px;
        border-radius: 8px;
        font-weight: 500;
    }
    .btn-outline-primary {
        border-width: 2px;
    }

    /* Enhanced member info styling */
    .member-info li {
        margin-bottom: 12px;
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;
    }

    .member-info li:last-child {
        border-bottom: none;
    }

    /* Section headers */
    .text-primary.border-bottom {
        border-color: #007bff !important;
        border-width: 2px !important;
    }

    /* Medical information styling */
    .bg-warning.bg-opacity-10 {
        background-color: rgba(255, 193, 7, 0.1) !important;
    }

    .bg-danger.bg-opacity-10 {
        background-color: rgba(220, 53, 69, 0.1) !important;
    }

    /* Social media links */
    .social-link {
        transition: all 0.3s ease;
        padding: 10px;
        border-radius: 8px;
    }

    .social-link:hover {
        background-color: #f8f9fa;
        transform: translateY(-2px);
    }

    /* Enhanced card styling */
    .card {
        border: none;
        box-shadow: 0 2px 10px rgba(0,0,0,0.08);
        border-radius: 12px;
    }

    .card-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 2px solid #dee2e6;
        border-radius: 12px 12px 0 0 !important;
    }

    /* Info boxes */
    .info-box {
        background: #f8f9fa;
        border-left: 4px solid #007bff;
        padding: 15px;
        border-radius: 0 8px 8px 0;
        margin: 10px 0;
    }

    .info-box.warning {
        border-left-color: #ffc107;
        background: #fff9e6;
    }

    .info-box.danger {
        border-left-color: #dc3545;
        background: #ffe6e6;
    }
</style>';

// Include header (which includes sidebar and necessary CSS/JS)
include 'includes/header.php';
?>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <?php
                // Fix profile image path display - same logic as members.php
                $profileImagePath = '';
                $showImage = false;

                if (!empty($member['image_path'])) {
                    $dbImagePath = $member['image_path'];

                    // Build the correct path for admin pages
                    if (strpos($dbImagePath, 'uploads/') === 0) {
                        // Path is 'uploads/profiles/filename.jpg' - add '../'
                        $relativePath = '../' . $dbImagePath;
                    } elseif (strpos($dbImagePath, '/uploads/') === 0) {
                        // Path is '/uploads/profiles/filename.jpg' - add '..'
                        $relativePath = '..' . $dbImagePath;
                    } else {
                        // Legacy format - assume it needs uploads prefix
                        $relativePath = '../uploads/profiles/' . basename($dbImagePath);
                    }

                    // Check if file exists using correct server path
                    $serverPath = __DIR__ . '/' . $relativePath;
                    if (file_exists($serverPath)) {
                        $profileImagePath = $relativePath;
                        $showImage = true;
                    }
                }
                ?>
                <?php if ($showImage): ?>
                <img src="<?php echo htmlspecialchars($profileImagePath); ?>"
                     alt="Profile Photo"
                     class="profile-image mb-3"
                     onerror="this.src='../assets/img/default-profile.jpg';"
                     title="<?php echo htmlspecialchars($member['full_name']); ?>">
                <?php else: ?>
                <div class="profile-image-placeholder mb-3 d-flex align-items-center justify-content-center bg-light">
                    <i class="bi bi-person-circle text-secondary" style="font-size: 5rem;"></i>
                </div>
                <?php endif; ?>
                <h4><?php echo htmlspecialchars($member['full_name']); ?></h4>
                <?php if (!empty($member['occupation'])): ?>
                <p class="text-muted"><?php echo htmlspecialchars($member['occupation']); ?></p>
                <?php endif; ?>
                <div class="mt-3">
                    <a href="mailto:<?php echo htmlspecialchars($member['email']); ?>" class="btn btn-sm btn-outline-primary me-2">
                        <i class="bi bi-envelope"></i> <?php _e('email'); ?>
                    </a>
                    <?php if (!empty($member['phone_number'])): ?>
                    <a href="tel:<?php echo htmlspecialchars($member['phone_number']); ?>" class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-telephone"></i> <?php _e('call'); ?>
                    </a>
                    <?php endif; ?>
                </div>
                
                <!-- Email Engagement -->
                <div class="engagement-stats">
                    <h6 class="text-muted text-center mb-3">
                        <i class="bi bi-envelope-check me-1"></i> Email Engagement
                    </h6>
                    <div class="text-center">
                        <div class="stats-number">
                            <?php echo isset($emailStats['total_sent']) ? number_format($emailStats['total_sent']) : '0'; ?>
                        </div>
                        <div class="stats-label"><?php _e('emails_sent'); ?></div>
                    </div>

                    <?php if (!empty($emailStats['last_opened'])): ?>
                    <div class="mt-3 text-center">
                        <small class="text-muted"><?php _e('last_email_opened'); ?>:</small><br>
                        <small><?php echo date('M d, Y g:i A', strtotime($emailStats['last_opened'])); ?></small>
                    </div>
                    <?php endif; ?>
                </div>
                
                <!-- Upcoming Birthday -->
                <?php if ($birthDate && $daysUntilBirthday !== null): ?>
                <div class="mt-4 pt-3 border-top">
                    <h6 class="text-muted text-center mb-3">
                        <i class="bi bi-gift me-1"></i> Birthday Information
                    </h6>
                    <div class="birthday-info">
                        <?php if ($daysUntilBirthday == 0): ?>
                            <div class="birthday-alert text-center"><?php _e('today_is_birthday'); ?> 🎉</div>
                        <?php elseif ($daysUntilBirthday == 1): ?>
                            <div class="birthday-alert text-center"><?php _e('birthday_tomorrow'); ?> 🎈</div>
                        <?php elseif ($daysUntilBirthday <= 7): ?>
                            <div class="birthday-alert text-center"><?php echo sprintf(__('birthday_in_days'), $daysUntilBirthday); ?> 🎂</div>
                        <?php endif; ?>

                        <div class="birthday-date text-center">
                            <strong><?php _e('next_birthday'); ?>:</strong><br>
                            <?php echo $upcomingBirthday; ?>
                        </div>

                        <div class="birthday-age text-center">
                            <strong><?php _e('age_on_next_birthday'); ?>:</strong><br>
                            <?php 
                                // Get birth year
                                $birthYear = date('Y', strtotime($member['birth_date']));
                                // Get year of next birthday
                                $nextBirthdayYear = date('Y', strtotime($upcomingBirthday));
                                // Calculate age they will be
                                $nextAge = $nextBirthdayYear - $birthYear;
                                echo $nextAge;
                            ?> years
                        </div>
                        
                        <a href="send_birthday.php?id=<?php echo $member_id; ?>" 
                           class="btn btn-outline-primary birthday-btn">
                            <i class="bi bi-send"></i> Send Birthday Message
                        </a>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><?php _e('quick_actions'); ?></h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <?php if (date('m') == date('m', strtotime($member['birth_date']))): ?>
                    <a href="send_birthday.php?id=<?php echo $member_id; ?>" class="btn btn-warning">
                        <i class="bi bi-gift"></i> <?php _e('send_birthday_message'); ?>
                    </a>
                    <?php endif; ?>
                    <a href="edit_member.php?id=<?php echo $member_id; ?>" class="btn btn-primary">
                        <i class="bi bi-pencil"></i> <?php _e('edit_member'); ?>
                    </a>
                    <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                        <i class="bi bi-trash"></i> <?php _e('delete_member'); ?>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <!-- Member Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><?php _e('member_information'); ?></h5>
            </div>
            <div class="card-body">
                <!-- Basic Information -->
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary border-bottom pb-2 mb-3">
                            <i class="bi bi-person-circle me-2"></i>Basic Information
                        </h6>
                        <ul class="member-info">
                            <li>
                                <i class="bi bi-person-fill text-primary me-2"></i>
                                <strong><?php _e('full_name'); ?>:</strong> <?php echo htmlspecialchars($member['full_name']); ?>
                            </li>
                            <li>
                                <i class="bi bi-envelope-fill text-primary me-2"></i>
                                <strong><?php _e('email'); ?>:</strong>
                                <a href="mailto:<?php echo htmlspecialchars($member['email']); ?>">
                                    <?php echo htmlspecialchars($member['email']); ?>
                                </a>
                            </li>
                            <li>
                                <i class="bi bi-telephone-fill text-primary me-2"></i>
                                <strong><?php _e('phone'); ?>:</strong>
                                <?php if (!empty($member['phone_number'])): ?>
                                    <a href="tel:<?php echo htmlspecialchars($member['phone_number']); ?>">
                                        <?php echo htmlspecialchars($member['phone_number']); ?>
                                    </a>
                                <?php else: ?>
                                    <span class="text-muted"><?php _e('not_provided'); ?></span>
                                <?php endif; ?>
                            </li>
                            <li>
                                <i class="bi bi-person-badge-fill text-primary me-2"></i>
                                <strong><?php _e('gender'); ?>:</strong>
                                <?php if (!empty($member['gender'])): ?>
                                    <span class="badge bg-<?php echo ($member['gender'] === 'Male') ? 'primary' : 'info'; ?> ms-2">
                                        <i class="bi bi-<?php echo ($member['gender'] === 'Male') ? 'person' : 'person-dress'; ?> me-1"></i>
                                        <?php echo htmlspecialchars($member['gender']); ?>
                                    </span>
                                <?php else: ?>
                                    <span class="text-muted"><?php _e('not_provided'); ?></span>
                                <?php endif; ?>
                            </li>
                            <li>
                                <i class="bi bi-calendar-date-fill text-primary me-2"></i>
                                <strong><?php _e('birth_date'); ?>:</strong>
                                <?php if (!empty($member['birth_date'])): ?>
                                    <?php echo date('F d, Y', strtotime($member['birth_date'])); ?>
                                    <span class="text-muted ms-2">
                                        <?php
                                        // Calculate current age based on birth date
                                        $birthDate = new DateTime($member['birth_date']);
                                        $now = new DateTime('now');
                                        $age = $birthDate->diff($now)->y;
                                        echo "(" . __('age') . ": $age)";
                                        ?>
                                    </span>
                                <?php else: ?>
                                    <span class="text-muted"><?php _e('not_provided'); ?></span>
                                <?php endif; ?>
                            </li>
                            <li>
                                <i class="bi bi-chat-dots-fill text-primary me-2"></i>
                                <strong>Preferred Communication:</strong>
                                <?php if (!empty($member['preferred_communication'])): ?>
                                    <span class="badge bg-info">
                                        <i class="bi bi-<?php
                                            echo $member['preferred_communication'] === 'email' ? 'envelope' :
                                                ($member['preferred_communication'] === 'phone' ? 'telephone' :
                                                ($member['preferred_communication'] === 'sms' ? 'chat-text' : 'whatsapp'));
                                        ?> me-1"></i>
                                        <?php echo ucfirst($member['preferred_communication']); ?>
                                    </span>
                                <?php else: ?>
                                    <span class="text-muted"><?php _e('not_provided'); ?></span>
                                <?php endif; ?>
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary border-bottom pb-2 mb-3">
                            <i class="bi bi-geo-alt-fill me-2"></i>Contact & Location
                        </h6>
                        <ul class="member-info">
                            <li>
                                <i class="bi bi-house-fill text-primary me-2"></i>
                                <strong><?php _e('address'); ?>:</strong>
                                <?php if (!empty($member['home_address'])): ?>
                                    <?php echo htmlspecialchars($member['home_address']); ?>
                                <?php else: ?>
                                    <span class="text-muted"><?php _e('not_provided'); ?></span>
                                <?php endif; ?>
                            </li>
                            <li>
                                <i class="bi bi-briefcase-fill text-primary me-2"></i>
                                <strong><?php _e('occupation'); ?>:</strong>
                                <?php if (!empty($member['occupation'])): ?>
                                    <?php echo htmlspecialchars($member['occupation']); ?>
                                <?php else: ?>
                                    <span class="text-muted"><?php _e('not_provided'); ?></span>
                                <?php endif; ?>
                            </li>
                            <li>
                                <i class="bi bi-calendar-check-fill text-primary me-2"></i>
                                <strong><?php _e('joined'); ?>:</strong> <?php echo date('F d, Y', strtotime($member['created_at'])); ?>
                                <span class="text-muted ms-2">
                                    (<?php
                                        $joinedDate = new DateTime($member['created_at']);
                                        $now = new DateTime();
                                        $interval = $joinedDate->diff($now);
                                        if ($interval->y > 0) {
                                            echo $interval->y . ' ' . ($interval->y > 1 ? __('years') : __('year'));
                                        } elseif ($interval->m > 0) {
                                            echo $interval->m . ' ' . ($interval->m > 1 ? __('months') : __('month'));
                                        } else {
                                            echo $interval->d . ' ' . ($interval->d > 1 ? __('days') : __('day'));
                                        }
                                    ?> <?php _e('ago'); ?>)
                                </span>
                            </li>
                            <li>
                                <i class="bi bi-clock-history text-primary me-2"></i>
                                <strong><?php _e('last_updated'); ?>:</strong>
                                <?php if (!empty($member['updated_at'])): ?>
                                    <?php echo date('F d, Y', strtotime($member['updated_at'])); ?>
                                <?php else: ?>
                                    <span class="text-muted"><?php _e('not_available'); ?></span>
                                <?php endif; ?>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Bio Section -->
                <?php if (!empty($member['bio'])): ?>
                <div class="mt-4">
                    <h6 class="text-primary border-bottom pb-2 mb-3">
                        <i class="bi bi-journal-text me-2"></i>Bio
                    </h6>
                    <div class="p-3 bg-light rounded">
                        <?php echo nl2br(htmlspecialchars($member['bio'])); ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Interests & Hobbies -->
                <?php if (!empty($member['interests'])): ?>
                <div class="mt-4">
                    <h6 class="text-primary border-bottom pb-2 mb-3">
                        <i class="bi bi-star-fill me-2"></i>Interests & Hobbies
                    </h6>
                    <div class="p-3 bg-light rounded">
                        <?php echo nl2br(htmlspecialchars($member['interests'])); ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Personal Information -->
                <div class="row mt-4">
                    <div class="col-md-6">
                        <h6 class="text-primary border-bottom pb-2 mb-3">
                            <i class="bi bi-person-heart me-2"></i>Personal Information
                        </h6>
                        <ul class="member-info">
                            <li>
                                <i class="bi bi-heart-fill text-primary me-2"></i>
                                <strong>Anniversary Date:</strong>
                                <?php if (!empty($member['anniversary_date'])): ?>
                                    <?php echo date('F d, Y', strtotime($member['anniversary_date'])); ?>
                                <?php else: ?>
                                    <span class="text-muted">Not provided</span>
                                <?php endif; ?>
                            </li>
                            <li>
                                <i class="bi bi-water text-primary me-2"></i>
                                <strong>Baptism Date:</strong>
                                <?php if (!empty($member['baptism_date'])): ?>
                                    <?php echo date('F d, Y', strtotime($member['baptism_date'])); ?>
                                <?php else: ?>
                                    <span class="text-muted">Not provided</span>
                                <?php endif; ?>
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary border-bottom pb-2 mb-3">
                            <i class="bi bi-clock-fill me-2"></i>Availability
                        </h6>
                        <ul class="member-info">
                            <li>
                                <i class="bi bi-calendar-week text-primary me-2"></i>
                                <strong>Availability Schedule:</strong>
                                <?php if (!empty($member['availability_schedule'])): ?>
                                    <div class="mt-1"><?php echo nl2br(htmlspecialchars($member['availability_schedule'])); ?></div>
                                <?php else: ?>
                                    <span class="text-muted">Not provided</span>
                                <?php endif; ?>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Volunteer Interests -->
                <?php if (!empty($member['volunteer_interests'])): ?>
                <div class="mt-4">
                    <h6 class="text-primary border-bottom pb-2 mb-3">
                        <i class="bi bi-hand-thumbs-up-fill me-2"></i>Volunteer Interests
                    </h6>
                    <div class="p-3 bg-light rounded">
                        <?php echo nl2br(htmlspecialchars($member['volunteer_interests'])); ?>
                    </div>
                </div>
                <?php endif; ?>



                <!-- Emergency Contact Information -->
                <div class="mt-4">
                    <h6 class="text-primary border-bottom pb-2 mb-3">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>Emergency Contact
                    </h6>
                    <div class="row">
                        <div class="col-md-4">
                            <ul class="member-info">
                                <li>
                                    <i class="bi bi-person-fill text-danger me-2"></i>
                                    <strong>Emergency Contact Name:</strong>
                                    <?php if (!empty($member['emergency_contact_name'])): ?>
                                        <?php echo htmlspecialchars($member['emergency_contact_name']); ?>
                                    <?php else: ?>
                                        <span class="text-muted">Not provided</span>
                                    <?php endif; ?>
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <ul class="member-info">
                                <li>
                                    <i class="bi bi-telephone-fill text-danger me-2"></i>
                                    <strong>Emergency Contact Phone:</strong>
                                    <?php if (!empty($member['emergency_contact_phone'])): ?>
                                        <a href="tel:<?php echo htmlspecialchars($member['emergency_contact_phone']); ?>">
                                            <?php echo htmlspecialchars($member['emergency_contact_phone']); ?>
                                        </a>
                                    <?php else: ?>
                                        <span class="text-muted">Not provided</span>
                                    <?php endif; ?>
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <ul class="member-info">
                                <li>
                                    <i class="bi bi-heart-fill text-danger me-2"></i>
                                    <strong>Relationship to Emergency Contact:</strong>
                                    <?php if (!empty($member['emergency_contact_relationship'])): ?>
                                        <?php echo htmlspecialchars($member['emergency_contact_relationship']); ?>
                                    <?php else: ?>
                                        <span class="text-muted">Not provided</span>
                                    <?php endif; ?>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Medical Information -->
                <div class="mt-4">
                    <h6 class="text-primary border-bottom pb-2 mb-3">
                        <i class="bi bi-heart-pulse-fill me-2"></i>Medical Information
                    </h6>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <strong><i class="bi bi-clipboard-pulse text-warning me-2"></i>Medical Conditions:</strong>
                            <?php if (!empty($member['medical_conditions'])): ?>
                                <div class="mt-2 p-3 bg-warning bg-opacity-10 border border-warning rounded">
                                    <?php echo nl2br(htmlspecialchars($member['medical_conditions'])); ?>
                                </div>
                            <?php else: ?>
                                <div class="mt-2 p-3 bg-light rounded text-muted">
                                    Not provided
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="col-md-6 mb-3">
                            <strong><i class="bi bi-shield-exclamation text-danger me-2"></i>Allergies:</strong>
                            <?php if (!empty($member['allergies'])): ?>
                                <div class="mt-2 p-3 bg-danger bg-opacity-10 border border-danger rounded">
                                    <?php echo nl2br(htmlspecialchars($member['allergies'])); ?>
                                </div>
                            <?php else: ?>
                                <div class="mt-2 p-3 bg-light rounded text-muted">
                                    Not provided
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <?php if (!empty($member['notes'])): ?>
                <div class="mt-4">
                    <h6 class="text-primary border-bottom pb-2 mb-3"><i class="bi bi-journal-text me-2"></i>Admin Notes</h6>
                    <div class="p-3 bg-light rounded"><?php echo nl2br(htmlspecialchars($member['notes'])); ?></div>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Social Media Links -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="bi bi-share me-2"></i>Social Media & Online Presence</h5>
            </div>
            <div class="card-body">
                <?php
                $socialLinks = !empty($member['social_media_links']) ? json_decode($member['social_media_links'], true) : [];
                if (!is_array($socialLinks)) {
                    $socialLinks = [];
                }
                ?>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-facebook text-primary me-3" style="font-size: 1.5rem;"></i>
                            <div>
                                <strong>Facebook:</strong><br>
                                <?php if (!empty($socialLinks['facebook'])): ?>
                                    <a href="<?php echo htmlspecialchars($socialLinks['facebook']); ?>" target="_blank" class="text-decoration-none">
                                        <?php echo htmlspecialchars($socialLinks['facebook']); ?>
                                        <i class="bi bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                <?php else: ?>
                                    <span class="text-muted">Not provided</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-twitter text-info me-3" style="font-size: 1.5rem;"></i>
                            <div>
                                <strong>Twitter:</strong><br>
                                <?php if (!empty($socialLinks['twitter'])): ?>
                                    <a href="<?php echo htmlspecialchars($socialLinks['twitter']); ?>" target="_blank" class="text-decoration-none">
                                        <?php echo htmlspecialchars($socialLinks['twitter']); ?>
                                        <i class="bi bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                <?php else: ?>
                                    <span class="text-muted">Not provided</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-instagram text-danger me-3" style="font-size: 1.5rem;"></i>
                            <div>
                                <strong>Instagram:</strong><br>
                                <?php if (!empty($socialLinks['instagram'])): ?>
                                    <a href="<?php echo htmlspecialchars($socialLinks['instagram']); ?>" target="_blank" class="text-decoration-none">
                                        <?php echo htmlspecialchars($socialLinks['instagram']); ?>
                                        <i class="bi bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                <?php else: ?>
                                    <span class="text-muted">Not provided</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-linkedin text-primary me-3" style="font-size: 1.5rem;"></i>
                            <div>
                                <strong>LinkedIn:</strong><br>
                                <?php if (!empty($socialLinks['linkedin'])): ?>
                                    <a href="<?php echo htmlspecialchars($socialLinks['linkedin']); ?>" target="_blank" class="text-decoration-none">
                                        <?php echo htmlspecialchars($socialLinks['linkedin']); ?>
                                        <i class="bi bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                <?php else: ?>
                                    <span class="text-muted">Not provided</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-globe text-success me-3" style="font-size: 1.5rem;"></i>
                            <div>
                                <strong>Personal Website:</strong><br>
                                <?php if (!empty($socialLinks['website'])): ?>
                                    <a href="<?php echo htmlspecialchars($socialLinks['website']); ?>" target="_blank" class="text-decoration-none">
                                        <?php echo htmlspecialchars($socialLinks['website']); ?>
                                        <i class="bi bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                <?php else: ?>
                                    <span class="text-muted">Not provided</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Message from Member -->
        <?php if (!empty($member['message'])): ?>
        <div class="card mb-4">
            <div class="card-header">
                <h5><?php _e('message_from_member'); ?></h5>
            </div>
            <div class="card-body">
                <p><?php echo nl2br(htmlspecialchars($member['message'])); ?></p>
            </div>
        </div>
        <?php endif; ?>
        
        <!-- Communication History -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><?php _e('communication_history'); ?></h5>
                <small class="text-muted">
                    <?php if ($total_logs > 0): ?>
                        Showing <?php echo $comm_offset + 1; ?>-<?php echo min($comm_offset + $comm_limit, $total_logs); ?> of <?php echo $total_logs; ?> communications
                    <?php endif; ?>
                </small>
            </div>
            <div class="card-body">
                <?php if (count($logs) > 0): ?>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th><?php _e('date'); ?></th>
                                <th><?php _e('subject'); ?></th>
                                <th><?php _e('template'); ?></th>
                                <th><?php _e('type'); ?></th>
                                <th><?php _e('status'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($logs as $log): ?>
                            <tr>
                                <td><?php echo date('M d, Y g:i A', strtotime($log['sent_at'])); ?></td>
                                <td class="text-nowrap text-truncate" style="max-width: 250px;" title="<?php echo htmlspecialchars($log['display_subject']); ?>">
                                    <?php echo htmlspecialchars($log['display_subject']); ?>
                                </td>
                                <td>
                                    <?php if (!empty($log['template_name'])): ?>
                                        <span class="badge bg-info text-dark"><?php echo htmlspecialchars($log['template_name']); ?></span>
                                    <?php else: ?>
                                        <span class="text-muted"><?php _e('unknown'); ?></span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php
                                    $emailType = $log['email_type'] ?? 'standard';
                                    $typeBadge = 'bg-secondary';
                                    $typeText = __('standard');

                                    if ($emailType == 'birthday') {
                                        $typeBadge = 'bg-primary';
                                        $typeText = __('birthday');
                                    } elseif ($emailType == 'reminder') {
                                        $typeBadge = 'bg-info text-dark';
                                        $typeText = __('reminder');
                                    } elseif ($emailType == 'b_notification' || $emailType == 'notification') {
                                        $typeBadge = 'bg-warning text-dark';
                                        $typeText = __('notification');
                                    } elseif ($emailType == 'welcome') {
                                        $typeBadge = 'bg-success';
                                        $typeText = __('welcome');
                                    }
                                    ?>
                                    <span class="badge <?php echo $typeBadge; ?>"><?php echo $typeText; ?></span>
                                </td>
                                <td>
                                    <?php
                                    // Check for success status (both 'sent' and 'success' mean success)
                                    if ($log['status'] == 'sent' || $log['status'] == 'success'):
                                    ?>
                                    <span class="badge bg-success"><?php _e('sent'); ?></span>
                                    <?php else: ?>
                                    <span class="badge bg-danger" data-bs-toggle="tooltip" title="<?php echo htmlspecialchars($log['error_message'] ?? __('unknown_error')); ?>">
                                        <?php _e('failed'); ?>
                                        <i class="bi bi-info-circle ms-1"></i>
                                    </span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Communication History Pagination -->
                <?php if ($total_comm_pages > 1): ?>
                <nav aria-label="Communication history pagination" class="mt-3">
                    <ul class="pagination pagination-sm justify-content-center">
                        <?php if ($comm_page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?id=<?php echo $member_id; ?>&comm_page=<?php echo $comm_page - 1; ?>">
                                    <i class="bi bi-chevron-left"></i> Previous
                                </a>
                            </li>
                        <?php endif; ?>

                        <?php for ($i = max(1, $comm_page - 2); $i <= min($total_comm_pages, $comm_page + 2); $i++): ?>
                            <li class="page-item <?php echo $i === $comm_page ? 'active' : ''; ?>">
                                <a class="page-link" href="?id=<?php echo $member_id; ?>&comm_page=<?php echo $i; ?>"><?php echo $i; ?></a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($comm_page < $total_comm_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?id=<?php echo $member_id; ?>&comm_page=<?php echo $comm_page + 1; ?>">
                                    Next <i class="bi bi-chevron-right"></i>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                    <div class="text-center mt-2">
                        <small class="text-muted">
                            Page <?php echo $comm_page; ?> of <?php echo $total_comm_pages; ?>
                        </small>
                    </div>
                </nav>
                <?php endif; ?>

                <?php else: ?>
                <div class="text-center py-4">
                    <i class="bi bi-chat-dots display-4 text-muted"></i>
                    <h6 class="mt-3 text-muted"><?php _e('no_communication_history'); ?></h6>
                    <p class="text-muted small">No email communications have been sent to this member yet.</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel"><?php _e('confirm_delete'); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <?php echo sprintf(__('confirm_delete_member_message'), htmlspecialchars($member['full_name'])); ?>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php _e('cancel'); ?></button>
                <a href="delete_member.php?id=<?php echo $member_id; ?>" class="btn btn-danger"><?php _e('delete'); ?></a>
            </div>
        </div>
    </div>
</div>



<?php
// The file should end with just the modals, scripts and closing body/html tags
include 'includes/footer.php';