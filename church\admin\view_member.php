<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Check if member ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: members.php");
    exit();
}

$member_id = intval($_GET['id']);

// Include the configuration file
require_once '../config.php';

// Include language system
require_once 'includes/language.php';

// Fix member term setting if it's empty (causing sidebar to show "s" instead of "Members")
function ensureMemberTermSetting() {
    global $pdo;

    try {
        // Check if member_term exists and has a value
        $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'member_term'");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$result || empty(trim($result['setting_value']))) {
            // Insert or update member_term with default value
            $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES ('member_term', 'Member')
                                  ON DUPLICATE KEY UPDATE setting_value = 'Member'");
            $stmt->execute();
            error_log("Fixed empty member_term setting - set to 'Member'");
        }
    } catch (PDOException $e) {
        error_log("Error fixing member_term setting: " . $e->getMessage());
    }
}

// Database connection - using the PDO connection from config.php

// Fix member term setting
ensureMemberTermSetting();

// Ensure all profile columns exist for detailed view
try {
    $columns_to_add = [
        'bio' => "TEXT NULL",
        'interests' => "TEXT NULL",
        'emergency_contact_name' => "VARCHAR(255) NULL",
        'emergency_contact_phone' => "VARCHAR(20) NULL",
        'emergency_contact_relationship' => "VARCHAR(100) NULL",
        'medical_conditions' => "TEXT NULL",
        'allergies' => "TEXT NULL",
        'preferred_communication' => "ENUM('email', 'phone', 'sms', 'whatsapp') DEFAULT 'email'",
        'anniversary_date' => "DATE NULL",
        'baptism_date' => "DATE NULL",
        'volunteer_interests' => "TEXT NULL",
        'availability_schedule' => "TEXT NULL",
        'social_media_links' => "JSON NULL"
    ];

    foreach ($columns_to_add as $column => $definition) {
        $stmt = $pdo->query("SHOW COLUMNS FROM members LIKE '$column'");
        if ($stmt->rowCount() == 0) {
            $pdo->exec("ALTER TABLE members ADD COLUMN $column $definition");
        }
    }
} catch (PDOException $e) {
    error_log("Error checking/adding member profile columns: " . $e->getMessage());
}

// Get member details
$stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
$stmt->execute([$member_id]);
$member = $stmt->fetch();

if (!$member) {
    header("Location: members.php");
    exit();
}

// Debug: Log what fields are available
error_log("Member data fields: " . implode(', ', array_keys($member)));
error_log("Bio field value: " . ($member['bio'] ?? 'NULL'));
error_log("Interests field value: " . ($member['interests'] ?? 'NULL'));

// Pagination for Communication History
$comm_page = isset($_GET['comm_page']) ? max(1, (int)$_GET['comm_page']) : 1;
$comm_limit = 5; // Reduced from 10 to 5
$comm_offset = ($comm_page - 1) * $comm_limit;

// Get total count of communication logs
$stmt = $pdo->prepare("SELECT COUNT(*) FROM email_logs WHERE member_id = ?");
$stmt->execute([$member_id]);
$total_logs = $stmt->fetchColumn();
$total_comm_pages = ceil($total_logs / $comm_limit);

// Get email logs for this member with pagination
$stmt = $pdo->prepare("
    SELECT el.*, et.template_name,
           COALESCE(el.subject, et.subject, 'No subject') as display_subject
    FROM email_logs el
    LEFT JOIN email_templates et ON el.template_id = et.id
    WHERE el.member_id = ?
    ORDER BY el.sent_at DESC
    LIMIT ? OFFSET ?
");
$stmt->execute([$member_id, $comm_limit, $comm_offset]);
$logs = $stmt->fetchAll();

// Get email tracking stats for this member
$stmt = $pdo->prepare("
    SELECT COUNT(*) as total_sent,
           COUNT(CASE WHEN opened_at IS NOT NULL THEN 1 END) as total_opened,
           MAX(opened_at) as last_opened,
           AVG(opened_count) as avg_opens
    FROM email_tracking
    WHERE member_id = ?
");
$stmt->execute([$member_id]);
$emailStats = $stmt->fetch();

// Get upcoming birthday info
$birthDate = !empty($member['birth_date']) ? new DateTime($member['birth_date']) : null;
$upcomingBirthday = null;
$daysUntilBirthday = null;

if ($birthDate) {
    // Use server date, not client date for consistency
    $today = new DateTime(date('Y-m-d')); // Today at midnight for correct day calculation
    
    // Get birthday this year
    $currentYearBirthday = new DateTime($today->format('Y') . '-' . $birthDate->format('m') . '-' . $birthDate->format('d'));
    
    // If already passed, get next year's birthday
    if ($currentYearBirthday < $today) {
        $currentYearBirthday->modify('+1 year');
    }
    
    $interval = $today->diff($currentYearBirthday);
    $daysUntilBirthday = $interval->days;
    $upcomingBirthday = $currentYearBirthday->format('F j, Y');
    
    // Log for debugging
    error_log("Today: " . $today->format('Y-m-d'));
    error_log("Birth Date: " . $birthDate->format('Y-m-d'));
    error_log("Current Year Birthday: " . $currentYearBirthday->format('Y-m-d'));
    error_log("Days Until Birthday: " . $daysUntilBirthday);
}

// Database connection will be closed automatically

// Set page title and header for header.php
$page_title = __('view_member');
$page_header = __('member_details');

// Extra CSS for this page
$extra_css = '
<style>
    /* Modern Profile Header */
    .profile-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 2rem 1.5rem;
        margin: 1rem 0 2rem 0;
        position: relative;
        overflow: hidden;
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.2);
    }

    .profile-header::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url("data:image/svg+xml,%3Csvg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"none\" fill-rule=\"evenodd\"%3E%3Cg fill=\"%23ffffff\" fill-opacity=\"0.1\"%3E%3Ccircle cx=\"30\" cy=\"30\" r=\"4\"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
        opacity: 0.3;
    }

    .profile-content {
        position: relative;
        z-index: 2;
    }

    .profile-image, .profile-image-placeholder {
        width: 140px;
        height: 140px;
        border-radius: 50%;
        object-fit: cover;
        border: 6px solid rgba(255,255,255,0.3);
        box-shadow: 0 10px 40px rgba(0,0,0,0.3);
        transition: transform 0.3s ease;
        margin: 0 auto 1.5rem;
    }

    .profile-image:hover {
        transform: scale(1.05);
    }

    .profile-image-placeholder {
        background: rgba(255,255,255,0.2);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Modern Cards */
    .modern-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        border: none;
        transition: all 0.3s ease;
        overflow: hidden;
        margin-bottom: 1.5rem;
    }

    .modern-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 30px rgba(0,0,0,0.12);
    }

    .modern-card-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: none;
        padding: 1.5rem;
        border-radius: 15px 15px 0 0;
        position: relative;
    }

    .modern-card-header::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 1.5rem;
        right: 1.5rem;
        height: 2px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 1px;
    }

    .modern-card-body {
        padding: 1.5rem;
    }

    .section-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #2c3e50;
        margin: 0;
        display: flex;
        align-items: center;
    }

    .section-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 0.75rem;
        font-size: 1.2rem;
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        box-shadow: 0 3px 10px rgba(102, 126, 234, 0.3);
    }
    /* Info Items */
    .info-item {
        display: flex;
        align-items: center;
        padding: 1rem;
        margin: 0.75rem 0;
        background: #f8f9fa;
        border-radius: 12px;
        transition: all 0.3s ease;
        border-left: 4px solid transparent;
        position: relative;
        overflow: hidden;
    }

    .info-item::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 5px;
        height: 100%;
        background: linear-gradient(135deg, #667eea, #764ba2);
        transform: scaleY(0);
        transition: transform 0.3s ease;
    }

    .info-item:hover {
        background: #e9ecef;
        transform: translateX(10px);
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .info-item:hover::before {
        transform: scaleY(1);
    }

    .info-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        font-size: 1.1rem;
        box-shadow: 0 3px 10px rgba(0,0,0,0.15);
        transition: transform 0.3s ease;
    }

    .info-item:hover .info-icon {
        transform: scale(1.1);
    }

    .info-icon.primary { background: linear-gradient(135deg, #007bff, #0056b3); color: white; }
    .info-icon.success { background: linear-gradient(135deg, #28a745, #1e7e34); color: white; }
    .info-icon.warning { background: linear-gradient(135deg, #ffc107, #e0a800); color: white; }
    .info-icon.danger { background: linear-gradient(135deg, #dc3545, #c82333); color: white; }
    .info-icon.info { background: linear-gradient(135deg, #17a2b8, #138496); color: white; }

    /* Stats Cards */
    .stat-card {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        text-align: center;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        border: none;
        position: relative;
        overflow: hidden;
    }

    .stat-card::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(135deg, #667eea, #764ba2);
    }

    .stat-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 50px rgba(0,0,0,0.15);
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 800;
        margin-bottom: 0.5rem;
        background: linear-gradient(135deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        line-height: 1;
    }

    .stat-label {
        font-size: 1rem;
        color: #6c757d;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    /* Action Buttons */
    .action-btn {
        border-radius: 30px;
        padding: 1rem 2rem;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 1px;
        transition: all 0.3s ease;
        border: none;
        position: relative;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    .action-btn::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: left 0.6s;
    }

    .action-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.3);
    }

    .action-btn:hover::before {
        left: 100%;
    }

    /* Custom Badges */
    .custom-badge {
        padding: 0.75rem 1.5rem;
        border-radius: 25px;
        font-weight: 700;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: 0 3px 10px rgba(0,0,0,0.2);
    }

    .badge-gradient-primary {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
    }

    .badge-gradient-success {
        background: linear-gradient(135deg, #28a745, #1e7e34);
        color: white;
    }

    .badge-gradient-warning {
        background: linear-gradient(135deg, #ffc107, #e0a800);
        color: white;
    }

    .badge-gradient-danger {
        background: linear-gradient(135deg, #dc3545, #c82333);
        color: white;
    }

    .badge-gradient-info {
        background: linear-gradient(135deg, #17a2b8, #138496);
        color: white;
    }

    /* Timeline */
    .timeline {
        position: relative;
        padding-left: 3rem;
    }

    .timeline::before {
        content: "";
        position: absolute;
        left: 1.5rem;
        top: 0;
        bottom: 0;
        width: 3px;
        background: linear-gradient(to bottom, #667eea, #764ba2);
        border-radius: 2px;
    }

    .timeline-item {
        position: relative;
        margin-bottom: 2.5rem;
        background: white;
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }

    .timeline-item:hover {
        transform: translateX(10px);
        box-shadow: 0 15px 40px rgba(0,0,0,0.15);
    }

    .timeline-item::before {
        content: "";
        position: absolute;
        left: -3.25rem;
        top: 2rem;
        width: 15px;
        height: 15px;
        border-radius: 50%;
        background: #667eea;
        border: 4px solid white;
        box-shadow: 0 0 0 4px #667eea;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .profile-header {
            padding: 2rem 1.5rem;
            text-align: center;
        }

        .modern-card-body {
            padding: 1.5rem;
        }

        .info-item {
            flex-direction: column;
            text-align: center;
            padding: 1.5rem;
        }

        .info-icon {
            margin-right: 0;
            margin-bottom: 1rem;
        }

        .timeline {
            padding-left: 2rem;
        }

        .timeline::before {
            left: 1rem;
        }

        .timeline-item::before {
            left: -2.25rem;
        }
    }

    /* Enhanced member info styling */
    .member-info {
        list-style: none;
        padding: 0;
    }

    .member-info li {
        margin-bottom: 1rem;
        padding: 0;
    }

    /* Medical information styling */
    .medical-card {
        background: linear-gradient(135deg, #fff9e6, #fff3cd);
        border: 2px solid #ffc107;
        border-radius: 20px;
        padding: 2rem;
        margin: 1rem 0;
        box-shadow: 0 10px 30px rgba(255, 193, 7, 0.2);
    }

    .allergy-card {
        background: linear-gradient(135deg, #ffe6e6, #ffebee);
        border: 2px solid #dc3545;
        border-radius: 20px;
        padding: 2rem;
        margin: 1rem 0;
        box-shadow: 0 10px 30px rgba(220, 53, 69, 0.2);
    }

    /* Social media links */
    .social-link {
        transition: all 0.3s ease;
        padding: 1rem;
        border-radius: 15px;
        display: inline-block;
        margin: 0.5rem;
        background: white;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        text-decoration: none;
    }

    .social-link:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        text-decoration: none;
    }

    /* Loading animation */
    .loading-shimmer {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: shimmer 2s infinite;
    }

    @keyframes shimmer {
        0% { background-position: -200% 0; }
        100% { background-position: 200% 0; }
    }
</style>';

// Include header (which includes sidebar and necessary CSS/JS)
include 'includes/header.php';
?>

<!-- Main Content Container -->
<div class="container-fluid">
    <!-- Modern Profile Header -->
    <div class="profile-header">
        <div class="profile-content">
            <div class="row align-items-center">
                <div class="col-md-3 text-center">
                <?php
                // Fix profile image path display - same logic as members.php
                $profileImagePath = '';
                $showImage = false;

                if (!empty($member['image_path'])) {
                    $dbImagePath = $member['image_path'];

                    // Build the correct path for admin pages
                    if (strpos($dbImagePath, 'uploads/') === 0) {
                        // Path is 'uploads/profiles/filename.jpg' - add '../'
                        $relativePath = '../' . $dbImagePath;
                    } elseif (strpos($dbImagePath, '/uploads/') === 0) {
                        // Path is '/uploads/profiles/filename.jpg' - add '..'
                        $relativePath = '..' . $dbImagePath;
                    } else {
                        // Legacy format - assume it needs uploads prefix
                        $relativePath = '../uploads/profiles/' . basename($dbImagePath);
                    }

                    // Check if file exists using correct server path
                    $serverPath = __DIR__ . '/' . $relativePath;
                    if (file_exists($serverPath)) {
                        $profileImagePath = $relativePath;
                        $showImage = true;
                    }
                }
                ?>
                <?php if ($showImage): ?>
                <img src="<?php echo htmlspecialchars($profileImagePath); ?>"
                     alt="Profile Photo"
                     class="profile-image"
                     onerror="this.src='../assets/img/default-profile.jpg';"
                     title="<?php echo htmlspecialchars($member['full_name']); ?>">
                <?php else: ?>
                <div class="profile-image-placeholder">
                    <i class="bi bi-person-circle" style="font-size: 4rem; color: rgba(255,255,255,0.8);"></i>
                </div>
                <?php endif; ?>
            </div>
            <div class="col-md-6">
                <h1 class="mb-2" style="font-size: 2.5rem; font-weight: 800;"><?php echo htmlspecialchars($member['full_name']); ?></h1>
                <?php if (!empty($member['occupation'])): ?>
                <h5 class="mb-3" style="opacity: 0.9; font-weight: 400;"><?php echo htmlspecialchars($member['occupation']); ?></h5>
                <?php endif; ?>
                <div class="d-flex flex-wrap gap-2 mb-3">
                    <?php if (!empty($member['gender'])): ?>
                    <span class="custom-badge badge-gradient-info">
                        <i class="bi bi-<?php echo ($member['gender'] === 'Male') ? 'person' : 'person-dress'; ?> me-1"></i>
                        <?php echo htmlspecialchars($member['gender']); ?>
                    </span>
                    <?php endif; ?>
                    <?php if (!empty($member['birth_date'])): ?>
                    <span class="custom-badge badge-gradient-success">
                        <i class="bi bi-calendar-heart me-1"></i>
                        <?php
                        $birthDate = new DateTime($member['birth_date']);
                        $now = new DateTime('now');
                        $age = $birthDate->diff($now)->y;
                        echo $age . " years old";
                        ?>
                    </span>
                    <?php endif; ?>
                    <span class="custom-badge badge-gradient-primary">
                        <i class="bi bi-person-check me-1"></i>
                        Active Member
                    </span>
                </div>
            </div>
            <div class="col-md-3 text-center">
                <div class="d-grid gap-2">
                    <a href="mailto:<?php echo htmlspecialchars($member['email']); ?>" class="action-btn btn btn-light">
                        <i class="bi bi-envelope me-2"></i>Send Email
                    </a>
                    <?php if (!empty($member['phone_number'])): ?>
                    <a href="tel:<?php echo htmlspecialchars($member['phone_number']); ?>" class="action-btn btn btn-outline-light">
                        <i class="bi bi-telephone me-2"></i>Call Now
                    </a>
                    <?php endif; ?>
                    <a href="edit_member.php?id=<?php echo $member['id']; ?>" class="action-btn btn btn-outline-light">
                        <i class="bi bi-pencil me-2"></i>Edit Profile
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Stats Overview -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stat-card">
            <div class="stat-number"><?php echo $emailStats['total_sent'] ?? 0; ?></div>
            <div class="stat-label">Emails Sent</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stat-card">
            <div class="stat-number"><?php echo $emailStats['total_opened'] ?? 0; ?></div>
            <div class="stat-label">Emails Opened</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stat-card">
            <div class="stat-number">
                <?php
                $openRate = ($emailStats['total_sent'] > 0) ? round(($emailStats['total_opened'] / $emailStats['total_sent']) * 100) : 0;
                echo $openRate . '%';
                ?>
            </div>
            <div class="stat-label">Open Rate</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stat-card">
            <div class="stat-number">
                <?php
                if (!empty($member['birth_date'])) {
                    $birthDate = new DateTime($member['birth_date']);
                    $today = new DateTime();
                    $nextBirthday = new DateTime($today->format('Y') . '-' . $birthDate->format('m-d'));
                    if ($nextBirthday < $today) {
                        $nextBirthday->add(new DateInterval('P1Y'));
                    }
                    $daysUntil = $today->diff($nextBirthday)->days;
                    echo $daysUntil;
                } else {
                    echo '-';
                }
                ?>
            </div>
            <div class="stat-label">Days to Birthday</div>
        </div>
    </div>
</div>

<div class="row">
                
                <!-- Email Engagement -->
                <div class="engagement-stats">
                    <h6 class="text-muted text-center mb-3">
                        <i class="bi bi-envelope-check me-1"></i> Email Engagement
                    </h6>
                    <div class="text-center">
                        <div class="stats-number">
                            <?php echo isset($emailStats['total_sent']) ? number_format($emailStats['total_sent']) : '0'; ?>
                        </div>
                        <div class="stats-label"><?php _e('emails_sent'); ?></div>
                    </div>

                    <?php if (!empty($emailStats['last_opened'])): ?>
                    <div class="mt-3 text-center">
                        <small class="text-muted"><?php _e('last_email_opened'); ?>:</small><br>
                        <small><?php echo date('M d, Y g:i A', strtotime($emailStats['last_opened'])); ?></small>
                    </div>
                    <?php endif; ?>
                </div>
                
                <!-- Upcoming Birthday -->
                <?php if ($birthDate && $daysUntilBirthday !== null): ?>
                <div class="mt-4 pt-3 border-top">
                    <h6 class="text-muted text-center mb-3">
                        <i class="bi bi-gift me-1"></i> Birthday Information
                    </h6>
                    <div class="birthday-info">
                        <?php if ($daysUntilBirthday == 0): ?>
                            <div class="birthday-alert text-center"><?php _e('today_is_birthday'); ?> 🎉</div>
                        <?php elseif ($daysUntilBirthday == 1): ?>
                            <div class="birthday-alert text-center"><?php _e('birthday_tomorrow'); ?> 🎈</div>
                        <?php elseif ($daysUntilBirthday <= 7): ?>
                            <div class="birthday-alert text-center"><?php echo sprintf(__('birthday_in_days'), $daysUntilBirthday); ?> 🎂</div>
                        <?php endif; ?>

                        <div class="birthday-date text-center">
                            <strong><?php _e('next_birthday'); ?>:</strong><br>
                            <?php echo $upcomingBirthday; ?>
                        </div>

                        <div class="birthday-age text-center">
                            <strong><?php _e('age_on_next_birthday'); ?>:</strong><br>
                            <?php 
                                // Get birth year
                                $birthYear = date('Y', strtotime($member['birth_date']));
                                // Get year of next birthday
                                $nextBirthdayYear = date('Y', strtotime($upcomingBirthday));
                                // Calculate age they will be
                                $nextAge = $nextBirthdayYear - $birthYear;
                                echo $nextAge;
                            ?> years
                        </div>
                        
                        <a href="send_birthday.php?id=<?php echo $member_id; ?>" 
                           class="btn btn-outline-primary birthday-btn">
                            <i class="bi bi-send"></i> Send Birthday Message
                        </a>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="modern-card mb-4">
            <div class="modern-card-header">
                <h5 class="section-title">
                    <div class="section-icon">
                        <i class="bi bi-lightning-charge"></i>
                    </div>
                    Quick Actions
                </h5>
            </div>
            <div class="modern-card-body">
                <div class="d-grid gap-3">
                    <a href="send_birthday.php?id=<?php echo $member_id; ?>" class="action-btn btn btn-primary">
                        <i class="bi bi-gift me-2"></i>Send Birthday Message
                    </a>
                    <a href="bulk_email.php?member_id=<?php echo $member_id; ?>" class="action-btn btn btn-info">
                        <i class="bi bi-envelope-plus me-2"></i>Send Custom Email
                    </a>
                    <a href="single_sms.php?member_id=<?php echo $member_id; ?>" class="action-btn btn btn-success">
                        <i class="bi bi-chat-text me-2"></i>Send SMS
                    </a>
                    <a href="send_gift_to_user.php?member_id=<?php echo $member_id; ?>" class="action-btn btn btn-warning">
                        <i class="bi bi-heart me-2"></i>Send Gift
                    </a>
                    <a href="edit_member.php?id=<?php echo $member_id; ?>" class="action-btn btn btn-secondary">
                        <i class="bi bi-pencil me-2"></i>Edit Profile
                    </a>
                    <button type="button" class="action-btn btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                        <i class="bi bi-trash me-2"></i>Delete Member
                    </button>
                </div>
            </div>
        </div>

        <!-- Engagement Stats -->
        <div class="modern-card">
            <div class="modern-card-header">
                <h5 class="section-title">
                    <div class="section-icon">
                        <i class="bi bi-graph-up"></i>
                    </div>
                    Engagement Stats
                </h5>
            </div>
            <div class="modern-card-body">
                <div class="info-item">
                    <div class="info-icon primary">
                        <i class="bi bi-envelope-check"></i>
                    </div>
                    <div>
                        <strong>Total Emails Sent</strong><br>
                        <span class="text-muted"><?php echo isset($emailStats['total_sent']) ? number_format($emailStats['total_sent']) : '0'; ?></span>
                    </div>
                </div>

                <div class="info-item">
                    <div class="info-icon success">
                        <i class="bi bi-envelope-open"></i>
                    </div>
                    <div>
                        <strong>Emails Opened</strong><br>
                        <span class="text-muted"><?php echo isset($emailStats['total_opened']) ? number_format($emailStats['total_opened']) : '0'; ?></span>
                    </div>
                </div>

                <?php if (!empty($emailStats['last_opened'])): ?>
                <div class="info-item">
                    <div class="info-icon info">
                        <i class="bi bi-clock-history"></i>
                    </div>
                    <div>
                        <strong>Last Email Opened</strong><br>
                        <span class="text-muted"><?php echo date('M d, Y g:i A', strtotime($emailStats['last_opened'])); ?></span>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="col-md-8">
        <!-- Communication History -->
        <div class="modern-card mb-4">
            <div class="modern-card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="section-title">
                        <div class="section-icon">
                            <i class="bi bi-chat-dots"></i>
                        </div>
                        Communication History
                    </h5>
                    <small class="text-muted">
                        <?php if ($total_logs > 0): ?>
                            Showing <?php echo $comm_offset + 1; ?>-<?php echo min($comm_offset + $comm_limit, $total_logs); ?> of <?php echo $total_logs; ?> communications
                        <?php endif; ?>
                    </small>
                </div>
            </div>
            <div class="modern-card-body">
                <?php if (count($logs) > 0): ?>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th><?php _e('date'); ?></th>
                                <th><?php _e('subject'); ?></th>
                                <th><?php _e('template'); ?></th>
                                <th><?php _e('type'); ?></th>
                                <th><?php _e('status'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($logs as $log): ?>
                            <tr>
                                <td><?php echo date('M d, Y g:i A', strtotime($log['sent_at'])); ?></td>
                                <td class="text-nowrap text-truncate" style="max-width: 250px;" title="<?php echo htmlspecialchars($log['display_subject']); ?>">
                                    <?php echo htmlspecialchars($log['display_subject']); ?>
                                </td>
                                <td>
                                    <?php if (!empty($log['template_name'])): ?>
                                        <span class="badge bg-info text-dark"><?php echo htmlspecialchars($log['template_name']); ?></span>
                                    <?php else: ?>
                                        <span class="text-muted"><?php _e('unknown'); ?></span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php
                                    $emailType = $log['email_type'] ?? 'standard';
                                    $typeBadge = 'bg-secondary';
                                    $typeText = __('standard');

                                    if ($emailType == 'birthday') {
                                        $typeBadge = 'bg-primary';
                                        $typeText = __('birthday');
                                    } elseif ($emailType == 'reminder') {
                                        $typeBadge = 'bg-info text-dark';
                                        $typeText = __('reminder');
                                    } elseif ($emailType == 'b_notification' || $emailType == 'notification') {
                                        $typeBadge = 'bg-warning text-dark';
                                        $typeText = __('notification');
                                    } elseif ($emailType == 'welcome') {
                                        $typeBadge = 'bg-success';
                                        $typeText = __('welcome');
                                    }
                                    ?>
                                    <span class="badge <?php echo $typeBadge; ?>"><?php echo $typeText; ?></span>
                                </td>
                                <td>
                                    <?php
                                    // Check for success status (both 'sent' and 'success' mean success)
                                    if ($log['status'] == 'sent' || $log['status'] == 'success'):
                                    ?>
                                    <span class="badge bg-success"><?php _e('sent'); ?></span>
                                    <?php else: ?>
                                    <span class="badge bg-danger" data-bs-toggle="tooltip" title="<?php echo htmlspecialchars($log['error_message'] ?? __('unknown_error')); ?>">
                                        <?php _e('failed'); ?>
                                        <i class="bi bi-info-circle ms-1"></i>
                                    </span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Communication History Pagination -->
                <?php if ($total_comm_pages > 1): ?>
                <nav aria-label="Communication history pagination" class="mt-3">
                    <ul class="pagination pagination-sm justify-content-center">
                        <?php if ($comm_page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?id=<?php echo $member_id; ?>&comm_page=<?php echo $comm_page - 1; ?>">
                                    <i class="bi bi-chevron-left"></i> Previous
                                </a>
                            </li>
                        <?php endif; ?>

                        <?php for ($i = max(1, $comm_page - 2); $i <= min($total_comm_pages, $comm_page + 2); $i++): ?>
                            <li class="page-item <?php echo $i === $comm_page ? 'active' : ''; ?>">
                                <a class="page-link" href="?id=<?php echo $member_id; ?>&comm_page=<?php echo $i; ?>"><?php echo $i; ?></a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($comm_page < $total_comm_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?id=<?php echo $member_id; ?>&comm_page=<?php echo $comm_page + 1; ?>">
                                    Next <i class="bi bi-chevron-right"></i>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                    <div class="text-center mt-2">
                        <small class="text-muted">
                            Page <?php echo $comm_page; ?> of <?php echo $total_comm_pages; ?>
                        </small>
                    </div>
                </nav>
                <?php endif; ?>

                <?php else: ?>
                <div class="text-center py-4">
                    <i class="bi bi-chat-dots display-4 text-muted"></i>
                    <h6 class="mt-3 text-muted"><?php _e('no_communication_history'); ?></h6>
                    <p class="text-muted small">No email communications have been sent to this member yet.</p>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Member Information -->
        <div class="modern-card mb-4">
            <div class="modern-card-header">
                <h5 class="section-title">
                    <div class="section-icon">
                        <i class="bi bi-person-badge"></i>
                    </div>
                    Member Information
                </h5>
            </div>
            <div class="modern-card-body">
                <!-- Basic Information -->
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="section-header">
                            <div class="section-icon">
                                <i class="bi bi-person-circle"></i>
                            </div>
                            Basic Information
                        </h6>

                        <div class="info-item">
                            <div class="info-icon primary">
                                <i class="bi bi-person-fill"></i>
                            </div>
                            <div>
                                <strong>Full Name</strong><br>
                                <span class="text-muted"><?php echo htmlspecialchars($member['full_name']); ?></span>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon primary">
                                <i class="bi bi-envelope-fill"></i>
                            </div>
                            <div>
                                <strong>Email Address</strong><br>
                                <a href="mailto:<?php echo htmlspecialchars($member['email']); ?>" class="text-decoration-none">
                                    <?php echo htmlspecialchars($member['email']); ?>
                                </a>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon primary">
                                <i class="bi bi-telephone-fill"></i>
                            </div>
                            <div>
                                <strong>Phone Number</strong><br>
                                <?php if (!empty($member['phone_number'])): ?>
                                    <a href="tel:<?php echo htmlspecialchars($member['phone_number']); ?>" class="text-decoration-none">
                                        <?php echo htmlspecialchars($member['phone_number']); ?>
                                    </a>
                                <?php else: ?>
                                    <span class="text-muted">Not provided</span>
                                <?php endif; ?>
                            </div>
                        </div>
                            <li>
                                <i class="bi bi-person-badge-fill text-primary me-2"></i>
                                <strong><?php _e('gender'); ?>:</strong>
                                <?php if (!empty($member['gender'])): ?>
                                    <span class="badge bg-<?php echo ($member['gender'] === 'Male') ? 'primary' : 'info'; ?> ms-2">
                                        <i class="bi bi-<?php echo ($member['gender'] === 'Male') ? 'person' : 'person-dress'; ?> me-1"></i>
                                        <?php echo htmlspecialchars($member['gender']); ?>
                                    </span>
                                <?php else: ?>
                                    <span class="text-muted"><?php _e('not_provided'); ?></span>
                                <?php endif; ?>
                            </li>
                            <li>
                                <i class="bi bi-calendar-date-fill text-primary me-2"></i>
                                <strong><?php _e('birth_date'); ?>:</strong>
                                <?php if (!empty($member['birth_date'])): ?>
                                    <?php echo date('F d, Y', strtotime($member['birth_date'])); ?>
                                    <span class="text-muted ms-2">
                                        <?php
                                        // Calculate current age based on birth date
                                        $birthDate = new DateTime($member['birth_date']);
                                        $now = new DateTime('now');
                                        $age = $birthDate->diff($now)->y;
                                        echo "(" . __('age') . ": $age)";
                                        ?>
                                    </span>
                                <?php else: ?>
                                    <span class="text-muted"><?php _e('not_provided'); ?></span>
                                <?php endif; ?>
                            </li>
                            <li>
                                <i class="bi bi-chat-dots-fill text-primary me-2"></i>
                                <strong>Preferred Communication:</strong>
                                <?php if (!empty($member['preferred_communication'])): ?>
                                    <span class="badge bg-info">
                                        <i class="bi bi-<?php
                                            echo $member['preferred_communication'] === 'email' ? 'envelope' :
                                                ($member['preferred_communication'] === 'phone' ? 'telephone' :
                                                ($member['preferred_communication'] === 'sms' ? 'chat-text' : 'whatsapp'));
                                        ?> me-1"></i>
                                        <?php echo ucfirst($member['preferred_communication']); ?>
                                    </span>
                                <?php else: ?>
                                    <span class="text-muted"><?php _e('not_provided'); ?></span>
                                <?php endif; ?>
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary border-bottom pb-2 mb-3">
                            <i class="bi bi-geo-alt-fill me-2"></i>Contact & Location
                        </h6>
                        <ul class="member-info">
                            <li>
                                <i class="bi bi-house-fill text-primary me-2"></i>
                                <strong><?php _e('address'); ?>:</strong>
                                <?php if (!empty($member['home_address'])): ?>
                                    <?php echo htmlspecialchars($member['home_address']); ?>
                                <?php else: ?>
                                    <span class="text-muted"><?php _e('not_provided'); ?></span>
                                <?php endif; ?>
                            </li>
                            <li>
                                <i class="bi bi-briefcase-fill text-primary me-2"></i>
                                <strong><?php _e('occupation'); ?>:</strong>
                                <?php if (!empty($member['occupation'])): ?>
                                    <?php echo htmlspecialchars($member['occupation']); ?>
                                <?php else: ?>
                                    <span class="text-muted"><?php _e('not_provided'); ?></span>
                                <?php endif; ?>
                            </li>
                            <li>
                                <i class="bi bi-calendar-check-fill text-primary me-2"></i>
                                <strong><?php _e('joined'); ?>:</strong> <?php echo date('F d, Y', strtotime($member['created_at'])); ?>
                                <span class="text-muted ms-2">
                                    (<?php
                                        $joinedDate = new DateTime($member['created_at']);
                                        $now = new DateTime();
                                        $interval = $joinedDate->diff($now);
                                        if ($interval->y > 0) {
                                            echo $interval->y . ' ' . ($interval->y > 1 ? __('years') : __('year'));
                                        } elseif ($interval->m > 0) {
                                            echo $interval->m . ' ' . ($interval->m > 1 ? __('months') : __('month'));
                                        } else {
                                            echo $interval->d . ' ' . ($interval->d > 1 ? __('days') : __('day'));
                                        }
                                    ?> <?php _e('ago'); ?>)
                                </span>
                            </li>
                            <li>
                                <i class="bi bi-clock-history text-primary me-2"></i>
                                <strong><?php _e('last_updated'); ?>:</strong>
                                <?php if (!empty($member['updated_at'])): ?>
                                    <?php echo date('F d, Y', strtotime($member['updated_at'])); ?>
                                <?php else: ?>
                                    <span class="text-muted"><?php _e('not_available'); ?></span>
                                <?php endif; ?>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Bio Section -->
                <div class="mt-4">
                    <h6 class="text-primary border-bottom pb-2 mb-3">
                        <i class="bi bi-journal-text me-2"></i>Bio
                    </h6>
                    <div class="p-3 bg-light rounded">
                        <?php if (!empty($member['bio'])): ?>
                            <?php echo nl2br(htmlspecialchars($member['bio'])); ?>
                        <?php else: ?>
                            <span class="text-muted">Not provided</span>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Interests & Hobbies -->
                <div class="mt-4">
                    <h6 class="text-primary border-bottom pb-2 mb-3">
                        <i class="bi bi-star-fill me-2"></i>Interests & Hobbies
                    </h6>
                    <div class="p-3 bg-light rounded">
                        <?php if (!empty($member['interests'])): ?>
                            <?php echo nl2br(htmlspecialchars($member['interests'])); ?>
                        <?php else: ?>
                            <span class="text-muted">Not provided</span>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Personal Information -->
                <div class="row mt-4">
                    <div class="col-md-6">
                        <h6 class="text-primary border-bottom pb-2 mb-3">
                            <i class="bi bi-person-heart me-2"></i>Personal Information
                        </h6>
                        <ul class="member-info">
                            <li>
                                <i class="bi bi-heart-fill text-primary me-2"></i>
                                <strong>Anniversary Date:</strong>
                                <?php if (!empty($member['anniversary_date'])): ?>
                                    <?php echo date('F d, Y', strtotime($member['anniversary_date'])); ?>
                                <?php else: ?>
                                    <span class="text-muted">Not provided</span>
                                <?php endif; ?>
                            </li>
                            <li>
                                <i class="bi bi-water text-primary me-2"></i>
                                <strong>Baptism Date:</strong>
                                <?php if (!empty($member['baptism_date'])): ?>
                                    <?php echo date('F d, Y', strtotime($member['baptism_date'])); ?>
                                <?php else: ?>
                                    <span class="text-muted">Not provided</span>
                                <?php endif; ?>
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary border-bottom pb-2 mb-3">
                            <i class="bi bi-clock-fill me-2"></i>Availability
                        </h6>
                        <ul class="member-info">
                            <li>
                                <i class="bi bi-calendar-week text-primary me-2"></i>
                                <strong>Availability Schedule:</strong>
                                <?php if (!empty($member['availability_schedule'])): ?>
                                    <div class="mt-1"><?php echo nl2br(htmlspecialchars($member['availability_schedule'])); ?></div>
                                <?php else: ?>
                                    <span class="text-muted">Not provided</span>
                                <?php endif; ?>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Volunteer Interests -->
                <div class="mt-4">
                    <h6 class="text-primary border-bottom pb-2 mb-3">
                        <i class="bi bi-hand-thumbs-up-fill me-2"></i>Volunteer Interests
                    </h6>
                    <div class="p-3 bg-light rounded">
                        <?php if (!empty($member['volunteer_interests'])): ?>
                            <?php echo nl2br(htmlspecialchars($member['volunteer_interests'])); ?>
                        <?php else: ?>
                            <span class="text-muted">Not provided</span>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Medical Information -->
                <div class="mt-4">
                    <h6 class="text-primary border-bottom pb-2 mb-3">
                        <i class="bi bi-heart-pulse-fill me-2"></i>Medical Information
                    </h6>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <strong><i class="bi bi-clipboard-pulse text-warning me-2"></i>Medical Conditions:</strong>
                            <?php if (!empty($member['medical_conditions'])): ?>
                                <div class="mt-2 p-3 bg-warning bg-opacity-10 border border-warning rounded">
                                    <?php echo nl2br(htmlspecialchars($member['medical_conditions'])); ?>
                                </div>
                            <?php else: ?>
                                <div class="mt-2 p-3 bg-light rounded text-muted">
                                    Not provided
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="col-md-6 mb-3">
                            <strong><i class="bi bi-shield-exclamation text-danger me-2"></i>Allergies:</strong>
                            <?php if (!empty($member['allergies'])): ?>
                                <div class="mt-2 p-3 bg-danger bg-opacity-10 border border-danger rounded">
                                    <?php echo nl2br(htmlspecialchars($member['allergies'])); ?>
                                </div>
                            <?php else: ?>
                                <div class="mt-2 p-3 bg-light rounded text-muted">
                                    Not provided
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Emergency Contact Information -->
                <div class="mt-4">
                    <h6 class="text-primary border-bottom pb-2 mb-3">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>Emergency Contact
                    </h6>
                    <div class="row">
                        <div class="col-md-4">
                            <ul class="member-info">
                                <li>
                                    <i class="bi bi-person-fill text-danger me-2"></i>
                                    <strong>Emergency Contact Name:</strong>
                                    <?php if (!empty($member['emergency_contact_name'])): ?>
                                        <?php echo htmlspecialchars($member['emergency_contact_name']); ?>
                                    <?php else: ?>
                                        <span class="text-muted">Not provided</span>
                                    <?php endif; ?>
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <ul class="member-info">
                                <li>
                                    <i class="bi bi-telephone-fill text-danger me-2"></i>
                                    <strong>Emergency Contact Phone:</strong>
                                    <?php if (!empty($member['emergency_contact_phone'])): ?>
                                        <a href="tel:<?php echo htmlspecialchars($member['emergency_contact_phone']); ?>">
                                            <?php echo htmlspecialchars($member['emergency_contact_phone']); ?>
                                        </a>
                                    <?php else: ?>
                                        <span class="text-muted">Not provided</span>
                                    <?php endif; ?>
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <ul class="member-info">
                                <li>
                                    <i class="bi bi-heart-fill text-danger me-2"></i>
                                    <strong>Relationship to Emergency Contact:</strong>
                                    <?php if (!empty($member['emergency_contact_relationship'])): ?>
                                        <?php echo htmlspecialchars($member['emergency_contact_relationship']); ?>
                                    <?php else: ?>
                                        <span class="text-muted">Not provided</span>
                                    <?php endif; ?>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <?php if (!empty($member['notes'])): ?>
                <div class="mt-4">
                    <h6 class="text-primary border-bottom pb-2 mb-3"><i class="bi bi-journal-text me-2"></i>Admin Notes</h6>
                    <div class="p-3 bg-light rounded"><?php echo nl2br(htmlspecialchars($member['notes'])); ?></div>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Social Media Links -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="bi bi-share me-2"></i>Social Media & Online Presence</h5>
            </div>
            <div class="card-body">
                <?php
                $socialLinks = !empty($member['social_media_links']) ? json_decode($member['social_media_links'], true) : [];
                if (!is_array($socialLinks)) {
                    $socialLinks = [];
                }
                ?>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-facebook text-primary me-3" style="font-size: 1.5rem;"></i>
                            <div>
                                <strong>Facebook:</strong><br>
                                <?php if (!empty($socialLinks['facebook'])): ?>
                                    <a href="<?php echo htmlspecialchars($socialLinks['facebook']); ?>" target="_blank" class="text-decoration-none">
                                        <?php echo htmlspecialchars($socialLinks['facebook']); ?>
                                        <i class="bi bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                <?php else: ?>
                                    <span class="text-muted">Not provided</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-twitter text-info me-3" style="font-size: 1.5rem;"></i>
                            <div>
                                <strong>Twitter:</strong><br>
                                <?php if (!empty($socialLinks['twitter'])): ?>
                                    <a href="<?php echo htmlspecialchars($socialLinks['twitter']); ?>" target="_blank" class="text-decoration-none">
                                        <?php echo htmlspecialchars($socialLinks['twitter']); ?>
                                        <i class="bi bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                <?php else: ?>
                                    <span class="text-muted">Not provided</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-instagram text-danger me-3" style="font-size: 1.5rem;"></i>
                            <div>
                                <strong>Instagram:</strong><br>
                                <?php if (!empty($socialLinks['instagram'])): ?>
                                    <a href="<?php echo htmlspecialchars($socialLinks['instagram']); ?>" target="_blank" class="text-decoration-none">
                                        <?php echo htmlspecialchars($socialLinks['instagram']); ?>
                                        <i class="bi bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                <?php else: ?>
                                    <span class="text-muted">Not provided</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-linkedin text-primary me-3" style="font-size: 1.5rem;"></i>
                            <div>
                                <strong>LinkedIn:</strong><br>
                                <?php if (!empty($socialLinks['linkedin'])): ?>
                                    <a href="<?php echo htmlspecialchars($socialLinks['linkedin']); ?>" target="_blank" class="text-decoration-none">
                                        <?php echo htmlspecialchars($socialLinks['linkedin']); ?>
                                        <i class="bi bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                <?php else: ?>
                                    <span class="text-muted">Not provided</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-globe text-success me-3" style="font-size: 1.5rem;"></i>
                            <div>
                                <strong>Personal Website:</strong><br>
                                <?php if (!empty($socialLinks['website'])): ?>
                                    <a href="<?php echo htmlspecialchars($socialLinks['website']); ?>" target="_blank" class="text-decoration-none">
                                        <?php echo htmlspecialchars($socialLinks['website']); ?>
                                        <i class="bi bi-box-arrow-up-right ms-1"></i>
                                    </a>
                                <?php else: ?>
                                    <span class="text-muted">Not provided</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Message from Member -->
        <?php if (!empty($member['message'])): ?>
        <div class="card mb-4">
            <div class="card-header">
                <h5><?php _e('message_from_member'); ?></h5>
            </div>
            <div class="card-body">
                <p><?php echo nl2br(htmlspecialchars($member['message'])); ?></p>
            </div>
        </div>
        <?php endif; ?>

    </div>
</div>
</div>
<!-- End Main Content Container -->

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel"><?php _e('confirm_delete'); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <?php echo sprintf(__('confirm_delete_member_message'), htmlspecialchars($member['full_name'])); ?>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php _e('cancel'); ?></button>
                <a href="delete_member.php?id=<?php echo $member_id; ?>" class="btn btn-danger"><?php _e('delete'); ?></a>
            </div>
        </div>
    </div>
</div>



<?php
// The file should end with just the modals, scripts and closing body/html tags
include 'includes/footer.php';