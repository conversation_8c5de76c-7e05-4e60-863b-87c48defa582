<?php
/**
 * Test script to debug comments endpoint
 */

// Start session and simulate login
session_start();
require_once 'config.php';

// Set up a test user session
$_SESSION['user_id'] = 29; // Ndivhuwo's ID
$_SESSION['username'] = '<EMAIL>';
$_SESSION['full_name'] = 'Ndivhuwo Machiba';

echo "<h2>Testing Comments Endpoint</h2>";

// Test different request IDs
$testRequestIds = [10, 9, 8, 7, 6];

foreach ($testRequestIds as $requestId) {
    echo "<h3>Testing Request ID: $requestId</h3>";
    
    // Capture output
    ob_start();
    
    // Set the request ID
    $_GET['request_id'] = $requestId;
    
    try {
        // Include the comments endpoint
        include 'user/ajax/get_request_comments.php';
        $output = ob_get_clean();
        
        echo "<strong>Output:</strong><br>";
        echo "<pre>" . htmlspecialchars($output) . "</pre>";
        
        // Try to decode as JSON
        $decoded = json_decode($output, true);
        if ($decoded) {
            echo "<strong>JSON Decoded Successfully:</strong><br>";
            echo "<pre>" . print_r($decoded, true) . "</pre>";
        } else {
            echo "<strong>JSON Decode Failed:</strong> " . json_last_error_msg() . "<br>";
        }
        
    } catch (Exception $e) {
        ob_end_clean();
        echo "<strong>Exception:</strong> " . $e->getMessage() . "<br>";
    }
    
    echo "<hr>";
}

// Also test the debug endpoint
echo "<h3>Testing Debug Endpoint for Request ID 10</h3>";
$_GET['request_id'] = 10;
ob_start();
try {
    include 'user/ajax/debug_request_access.php';
    $output = ob_get_clean();
    echo "<strong>Debug Output:</strong><br>";
    echo "<pre>" . htmlspecialchars($output) . "</pre>";
} catch (Exception $e) {
    ob_end_clean();
    echo "<strong>Debug Exception:</strong> " . $e->getMessage() . "<br>";
}
?>
