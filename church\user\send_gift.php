<?php
/**
 * User Gift Sending Interface
 * Allows members to send gifts to other members
 */

require_once '../config.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include classes
require_once '../classes/SecurityManager.php';
require_once '../classes/UserAuthManager.php';
require_once '../includes/enhanced_activity_system.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Set security headers
$security->setSecurityHeaders();

// Check if user is authenticated
if (!$userAuth->isAuthenticated()) {
    header("Location: login.php");
    exit();
}

$userId = $_SESSION['user_id'];

// Create member_gifts table if it doesn't exist
try {
    $pdo->exec("CREATE TABLE IF NOT EXISTS member_gifts (
        id INT AUTO_INCREMENT PRIMARY KEY,
        sender_id INT NULL,
        recipient_id INT NOT NULL,
        gift_type VARCHAR(50) NOT NULL,
        gift_title VARCHAR(255) NOT NULL,
        gift_message TEXT,
        gift_file_path VARCHAR(500),
        delivery_date DATE,
        is_anonymous BOOLEAN DEFAULT FALSE,
        is_delivered BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        delivered_at TIMESTAMP NULL,
        INDEX idx_sender (sender_id),
        INDEX idx_recipient (recipient_id),
        INDEX idx_delivery_date (delivery_date),
        FOREIGN KEY (recipient_id) REFERENCES members(id) ON DELETE CASCADE
    )");

    // Try to modify existing table to allow NULL sender_id
    try {
        $pdo->exec("ALTER TABLE member_gifts MODIFY COLUMN sender_id INT NULL");
        $pdo->exec("ALTER TABLE member_gifts DROP FOREIGN KEY member_gifts_ibfk_1");
    } catch (PDOException $e) {
        // Ignore errors if constraints don't exist or table is already correct
    }
} catch (PDOException $e) {
    // Table might already exist, continue
}

// Get current user data
$stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$userData = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$userData) {
    header('Location: login.php');
    exit();
}

$message = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['send_gift'])) {
    try {
        // Handle file upload
        $uploaded_file = null;
        if (isset($_FILES['gift_file']) && $_FILES['gift_file']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = '../uploads/gifts/';
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            $file_extension = pathinfo($_FILES['gift_file']['name'], PATHINFO_EXTENSION);
            $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'];
            
            if (in_array(strtolower($file_extension), $allowed_extensions)) {
                $filename = uniqid() . '.' . $file_extension;
                $upload_path = $upload_dir . $filename;
                
                if (move_uploaded_file($_FILES['gift_file']['tmp_name'], $upload_path)) {
                    $uploaded_file = 'uploads/gifts/' . $filename;
                }
            } else {
                throw new Exception('Invalid file type. Please upload images, PDFs, or documents only.');
            }
        }
        
        // Insert gift record
        $stmt = $pdo->prepare("
            INSERT INTO member_gifts (
                sender_id, recipient_id, gift_type, gift_title, gift_message, 
                gift_file_path, delivery_date, is_anonymous, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $delivery_date = $_POST['delivery_date'] ?: date('Y-m-d');
        
        $stmt->execute([
            $_SESSION['user_id'],
            $_POST['recipient_id'],
            $_POST['gift_type'],
            $_POST['gift_title'],
            $_POST['gift_message'],
            $uploaded_file,
            $delivery_date,
            isset($_POST['is_anonymous']) ? 1 : 0
        ]);
        
        $gift_id = $pdo->lastInsertId();
        
        // Send email notification if delivery is immediate
        if ($delivery_date <= date('Y-m-d')) {
            // Get recipient details
            $stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
            $stmt->execute([$_POST['recipient_id']]);
            $recipient = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($recipient && $recipient['email']) {
                $sender_name = isset($_POST['is_anonymous']) && $_POST['is_anonymous'] ? 'Anonymous' : $userData['full_name'];
                
                $subject = "🎁 You've received a gift!";
                $email_body = "
                <h2>You've received a gift!</h2>
                <p>Dear {$recipient['full_name']},</p>
                <p>You've received a special gift from {$sender_name}!</p>
                <p><strong>Gift:</strong> {$_POST['gift_title']}</p>
                <p><strong>Message:</strong> {$_POST['gift_message']}</p>
                ";
                
                if ($uploaded_file) {
                    $email_body .= "<p><strong>Attachment:</strong> Please check your member portal for the attached gift file.</p>";
                }
                
                $email_body .= "
                <p>Login to your member portal to view your gift!</p>
                <p>With warm regards,<br>Church Management Team</p>
                ";
                
                // Send email using existing email system
                try {
                    if (function_exists('sendEmail')) {
                        $email_sent = sendEmail(
                            $recipient['email'],
                            $recipient['full_name'],
                            $subject,
                            $email_body,
                            true
                        );

                        if (!$email_sent) {
                            error_log("Failed to send gift notification email to " . $recipient['email']);
                        }
                    }
                } catch (Exception $e) {
                    error_log("Error sending gift notification email: " . $e->getMessage());
                }
            }
        }

        // Log gift sending activity
        global $activityTracker;
        $activityTracker->logActivity(
            $_SESSION['user_id'],
            'gift_sent',
            "Sent a gift '{$_POST['gift_title']}' to " . $recipient['full_name'],
            'gift',
            $gift_id,
            $_POST['recipient_id'],
            [
                'gift_title' => $_POST['gift_title'],
                'gift_type' => $_POST['gift_type'],
                'delivery_date' => $delivery_date,
                'is_anonymous' => isset($_POST['is_anonymous']) ? 1 : 0
            ]
        );

        $message = "Gift sent successfully! " . ($delivery_date > date('Y-m-d') ? "It will be delivered on " . date('F j, Y', strtotime($delivery_date)) : "The recipient has been notified.");
        
    } catch (Exception $e) {
        $error = "Error sending gift: " . $e->getMessage();
    }
}

// Get all members for recipient selection (excluding current user)
$stmt = $pdo->prepare("
    SELECT id, full_name, first_name, last_name, birth_date 
    FROM members 
    WHERE id != ? AND status = 'active'
    ORDER BY full_name
");
$stmt->execute([$_SESSION['user_id']]);
$members = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get upcoming birthdays (next 30 days)
$stmt = $pdo->prepare("
    SELECT id, full_name, birth_date,
           CASE 
               WHEN DATE_FORMAT(birth_date, '%m-%d') >= DATE_FORMAT(CURDATE(), '%m-%d')
               THEN DATEDIFF(DATE(CONCAT(YEAR(CURDATE()), '-', DATE_FORMAT(birth_date, '%m-%d'))), CURDATE())
               ELSE DATEDIFF(DATE(CONCAT(YEAR(CURDATE()) + 1, '-', DATE_FORMAT(birth_date, '%m-%d'))), CURDATE())
           END as days_until_birthday
    FROM members 
    WHERE id != ? AND status = 'active' AND birth_date IS NOT NULL
    HAVING days_until_birthday <= 30
    ORDER BY days_until_birthday
");
$stmt->execute([$_SESSION['user_id']]);
$upcoming_birthdays = $stmt->fetchAll(PDO::FETCH_ASSOC);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Send Gift - <?php echo get_organization_name(); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">

    <!-- Custom Theme CSS from Appearance Settings -->
    <?php include 'includes/theme_css.php'; ?>

    <style>
        body {
            background-color: var(--bs-body-bg, #f8f9fa);
            color: var(--bs-body-color, #212529);
            font-family: var(--bs-font-sans-serif, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif);
        }

        .navbar {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 700;
            color: white !important;
            display: flex;
            align-items: center;
        }

        .navbar-logo {
            max-height: 40px;
            max-width: 150px;
            height: auto;
            width: auto;
            object-fit: contain;
        }

        /* Responsive logo adjustments */
        @media (max-width: 768px) {
            .navbar-logo {
                max-height: 32px;
                max-width: 120px;
            }

            .navbar-brand {
                font-size: 1rem;
            }
        }

        .navbar-nav .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .navbar-nav .nav-link:hover {
            color: white !important;
        }

        .dashboard-container {
            margin-top: 2rem;
        }

        .dashboard-card {
            background: var(--bs-body-bg, white);
            border-radius: var(--bs-border-radius, 15px);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border: none;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .dashboard-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.12);
        }

        .dashboard-card h5 {
            color: var(--bs-body-color, #2c3e50);
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            border: none;
            border-radius: var(--bs-border-radius, 10px);
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
        }

        .alert-success {
            border-radius: var(--bs-border-radius, 12px);
            border: none;
            background-color: var(--bs-success, #d4edda);
            color: var(--bs-body-color, #155724);
        }

        .birthday-card {
            border-left: 4px solid #ffc107;
            background: linear-gradient(135deg, #fff3cd 0%, #ffffff 100%);
        }
        .gift-preview {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            background: #f8f9fa;
        }
        .file-upload-area {
            border: 2px dashed #007bff;
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .file-upload-area:hover {
            background: #f8f9fa;
            border-color: #0056b3;
        }
        .file-upload-area.dragover {
            background: #e3f2fd;
            border-color: #1976d2;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <div class="container dashboard-container">
        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <!-- Upcoming Birthdays -->
            <div class="col-md-4">
                <div class="dashboard-card">
                    <h5><i class="bi bi-gift"></i> Upcoming Birthdays</h5>
                    <?php if (empty($upcoming_birthdays)): ?>
                        <p class="text-muted">No upcoming birthdays in the next 30 days.</p>
                    <?php else: ?>
                        <?php foreach ($upcoming_birthdays as $birthday): ?>
                            <div class="birthday-card card mb-2">
                                <div class="card-body py-2">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong><?php echo htmlspecialchars($birthday['full_name']); ?></strong>
                                            <br><small class="text-muted">
                                                <?php echo date('M j', strtotime($birthday['birth_date'])); ?>
                                                (<?php echo $birthday['days_until_birthday']; ?> days)
                                            </small>
                                        </div>
                                        <button class="btn btn-sm btn-warning" onclick="selectRecipient(<?php echo $birthday['id']; ?>, '<?php echo htmlspecialchars($birthday['full_name']); ?>')">
                                            <i class="bi bi-gift"></i> Send Gift
                                        </button>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Gift Sending Form -->
            <div class="col-md-8">
                <div class="dashboard-card">
                    <h5><i class="bi bi-send"></i> Send a Gift</h5>
                    <form method="POST" enctype="multipart/form-data" id="giftForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="recipient_id" class="form-label">Recipient *</label>
                                    <select class="form-select" id="recipient_id" name="recipient_id" required>
                                        <option value="">Select a member...</option>
                                        <?php foreach ($members as $member): ?>
                                            <option value="<?php echo $member['id']; ?>">
                                                <?php echo htmlspecialchars($member['full_name']); ?>
                                                <?php if ($member['birth_date']): ?>
                                                    (Birthday: <?php echo date('M j', strtotime($member['birth_date'])); ?>)
                                                <?php endif; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="gift_type" class="form-label">Gift Type *</label>
                                    <select class="form-select" id="gift_type" name="gift_type" required>
                                        <option value="">Select gift type...</option>
                                        <option value="digital_card">Digital Card</option>
                                        <option value="gift_card">Gift Card</option>
                                        <option value="document">Document/Certificate</option>
                                        <option value="photo">Photo/Image</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="gift_title" class="form-label">Gift Title *</label>
                            <input type="text" class="form-control" id="gift_title" name="gift_title"
                                   placeholder="e.g., Happy Birthday Card, Gift Certificate, etc." required>
                        </div>

                        <div class="mb-3">
                            <label for="gift_message" class="form-label">Personal Message *</label>
                            <textarea class="form-control" id="gift_message" name="gift_message" rows="4"
                                      placeholder="Write a personal message to accompany your gift..." required></textarea>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Upload Gift File (Optional)</label>
                            <div class="file-upload-area" onclick="document.getElementById('gift_file').click()">
                                <i class="bi bi-cloud-upload" style="font-size: 2rem; color: #007bff;"></i>
                                <p class="mb-0 mt-2">Click to upload or drag and drop</p>
                                <small class="text-muted">Images, PDFs, Documents (Max 10MB)</small>
                            </div>
                            <input type="file" class="form-control d-none" id="gift_file" name="gift_file"
                                   accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx">
                            <div id="file-preview" class="mt-2"></div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="delivery_date" class="form-label">Delivery Date</label>
                                    <input type="date" class="form-control" id="delivery_date" name="delivery_date"
                                           min="<?php echo date('Y-m-d'); ?>" value="<?php echo date('Y-m-d'); ?>">
                                    <small class="text-muted">Leave as today for immediate delivery</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check mt-4">
                                        <input class="form-check-input" type="checkbox" id="is_anonymous" name="is_anonymous">
                                        <label class="form-check-label" for="is_anonymous">
                                            Send anonymously
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" name="send_gift" class="btn btn-primary btn-lg">
                                <i class="bi bi-send"></i> Send Gift
                            </button>
                        </div>
                    </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function selectRecipient(memberId, memberName) {
            document.getElementById('recipient_id').value = memberId;
            
            // Auto-fill birthday-related fields
            document.getElementById('gift_type').value = 'digital_card';
            document.getElementById('gift_title').value = 'Happy Birthday!';
            document.getElementById('gift_message').value = `Dear ${memberName},\n\nWishing you a wonderful birthday filled with joy, blessings, and happiness!\n\nWith warm wishes,\n${<?php echo json_encode($userData['full_name']); ?>}`;
        }

        // File upload handling
        const fileInput = document.getElementById('gift_file');
        const filePreview = document.getElementById('file-preview');
        const uploadArea = document.querySelector('.file-upload-area');

        fileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                filePreview.innerHTML = `
                    <div class="alert alert-info">
                        <i class="bi bi-file-earmark"></i> Selected: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)
                    </div>
                `;
            }
        });

        // Drag and drop functionality
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                fileInput.dispatchEvent(new Event('change'));
            }
        });
    </script>

    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>
</body>
</html>
