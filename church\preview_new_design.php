<?php
// Preview of the new modern design for view_member.php
require_once 'config.php';

// Get sample member data (<PERSON><PERSON> 51)
$stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
$stmt->execute([51]);
$member = $stmt->fetch();

if (!$member) {
    echo "Member not found!";
    exit;
}

// Sample stats
$emailStats = [
    'total_sent' => 25,
    'total_opened' => 18,
    'last_opened' => '2025-07-15 14:30:00'
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Member View - Preview</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        
        /* Modern Profile Header */
        .profile-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 25px;
            padding: 3rem 2rem;
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
        }

        .profile-header::before {
            content: "";
            position: absolute;
            top: 0; left: 0; right: 0; bottom: 0;
            background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
            opacity: 0.3;
        }

        .profile-content { position: relative; z-index: 2; }

        .profile-image {
            width: 140px; height: 140px; border-radius: 50%; object-fit: cover;
            border: 6px solid rgba(255,255,255,0.3);
            box-shadow: 0 10px 40px rgba(0,0,0,0.3);
            transition: transform 0.3s ease;
        }

        .profile-image:hover { transform: scale(1.05); }

        /* Modern Cards */
        .modern-card {
            background: white; border-radius: 25px;
            box-shadow: 0 15px 50px rgba(0,0,0,0.1);
            border: none; transition: all 0.3s ease;
            overflow: hidden; margin-bottom: 2rem;
        }

        .modern-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 70px rgba(0,0,0,0.15);
        }

        .modern-card-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-bottom: none; padding: 2rem;
            border-radius: 25px 25px 0 0; position: relative;
        }

        .modern-card-header::after {
            content: ""; position: absolute; bottom: 0; left: 2rem; right: 2rem;
            height: 3px; background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .modern-card-body { padding: 2.5rem; }

        .section-title {
            font-size: 1.5rem; font-weight: 700; color: #2c3e50;
            margin: 0; display: flex; align-items: center;
        }

        .section-icon {
            width: 50px; height: 50px; border-radius: 15px;
            display: flex; align-items: center; justify-content: center;
            margin-right: 1rem; font-size: 1.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white; box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        /* Info Items */
        .info-item {
            display: flex; align-items: center; padding: 1.5rem;
            margin: 1rem 0; background: #f8f9fa; border-radius: 20px;
            transition: all 0.3s ease; border-left: 5px solid transparent;
            position: relative; overflow: hidden;
        }

        .info-item::before {
            content: ""; position: absolute; top: 0; left: 0;
            width: 5px; height: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            transform: scaleY(0); transition: transform 0.3s ease;
        }

        .info-item:hover {
            background: #e9ecef; transform: translateX(10px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .info-item:hover::before { transform: scaleY(1); }

        .info-icon {
            width: 50px; height: 50px; border-radius: 15px;
            display: flex; align-items: center; justify-content: center;
            margin-right: 1.5rem; font-size: 1.3rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            transition: transform 0.3s ease;
        }

        .info-item:hover .info-icon { transform: scale(1.1); }

        .info-icon.primary { background: linear-gradient(135deg, #007bff, #0056b3); color: white; }
        .info-icon.success { background: linear-gradient(135deg, #28a745, #1e7e34); color: white; }
        .info-icon.warning { background: linear-gradient(135deg, #ffc107, #e0a800); color: white; }
        .info-icon.danger { background: linear-gradient(135deg, #dc3545, #c82333); color: white; }
        .info-icon.info { background: linear-gradient(135deg, #17a2b8, #138496); color: white; }

        /* Stats Cards */
        .stat-card {
            background: white; border-radius: 20px; padding: 2rem;
            text-align: center; box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease; border: none;
            position: relative; overflow: hidden;
        }

        .stat-card::before {
            content: ""; position: absolute; top: 0; left: 0; right: 0;
            height: 4px; background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .stat-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 50px rgba(0,0,0,0.15);
        }

        .stat-number {
            font-size: 2.5rem; font-weight: 800; margin-bottom: 0.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text; -webkit-text-fill-color: transparent;
            background-clip: text; line-height: 1;
        }

        .stat-label {
            font-size: 1rem; color: #6c757d; font-weight: 600;
            text-transform: uppercase; letter-spacing: 1px;
        }

        /* Action Buttons */
        .action-btn {
            border-radius: 30px; padding: 1rem 2rem; font-weight: 700;
            text-transform: uppercase; letter-spacing: 1px;
            transition: all 0.3s ease; border: none;
            position: relative; overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.3);
        }

        /* Custom Badges */
        .custom-badge {
            padding: 0.75rem 1.5rem; border-radius: 25px;
            font-weight: 700; font-size: 0.9rem;
            text-transform: uppercase; letter-spacing: 0.5px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
        }

        .badge-gradient-primary { background: linear-gradient(135deg, #007bff, #0056b3); color: white; }
        .badge-gradient-success { background: linear-gradient(135deg, #28a745, #1e7e34); color: white; }
        .badge-gradient-warning { background: linear-gradient(135deg, #ffc107, #e0a800); color: white; }
        .badge-gradient-info { background: linear-gradient(135deg, #17a2b8, #138496); color: white; }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row justify-content-center">
            <div class="col-12 col-xl-10">
                <!-- Modern Profile Header -->
                <div class="profile-header">
                    <div class="profile-content">
                        <div class="row align-items-center">
                            <div class="col-md-3 text-center">
                                <div class="profile-image-placeholder d-flex align-items-center justify-content-center mx-auto" style="width: 140px; height: 140px; border-radius: 50%; border: 6px solid rgba(255,255,255,0.3); background: rgba(255,255,255,0.2);">
                                    <i class="bi bi-person-circle" style="font-size: 4rem; color: rgba(255,255,255,0.8);"></i>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h1 class="mb-2" style="font-size: 2.5rem; font-weight: 800;"><?php echo htmlspecialchars($member['full_name']); ?></h1>
                                <h5 class="mb-3" style="opacity: 0.9; font-weight: 400;"><?php echo htmlspecialchars($member['occupation'] ?? 'Member'); ?></h5>
                                <div class="d-flex flex-wrap gap-2 mb-3">
                                    <span class="custom-badge badge-gradient-info">
                                        <i class="bi bi-person me-1"></i><?php echo htmlspecialchars($member['gender'] ?? 'Not specified'); ?>
                                    </span>
                                    <span class="custom-badge badge-gradient-success">
                                        <i class="bi bi-calendar-heart me-1"></i>
                                        <?php
                                        if (!empty($member['birth_date'])) {
                                            $birthDate = new DateTime($member['birth_date']);
                                            $now = new DateTime('now');
                                            $age = $birthDate->diff($now)->y;
                                            echo $age . " years old";
                                        } else {
                                            echo "Age not provided";
                                        }
                                        ?>
                                    </span>
                                    <span class="custom-badge badge-gradient-primary">
                                        <i class="bi bi-person-check me-1"></i>Active Member
                                    </span>
                                </div>
                            </div>
                            <div class="col-md-3 text-center">
                                <div class="d-grid gap-2">
                                    <a href="mailto:<?php echo htmlspecialchars($member['email']); ?>" class="action-btn btn btn-light">
                                        <i class="bi bi-envelope me-2"></i>Send Email
                                    </a>
                                    <a href="tel:<?php echo htmlspecialchars($member['phone_number'] ?? ''); ?>" class="action-btn btn btn-outline-light">
                                        <i class="bi bi-telephone me-2"></i>Call Now
                                    </a>
                                    <a href="#" class="action-btn btn btn-outline-light">
                                        <i class="bi bi-pencil me-2"></i>Edit Profile
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Stats Overview -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-number"><?php echo $emailStats['total_sent']; ?></div>
                            <div class="stat-label">Emails Sent</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-number"><?php echo $emailStats['total_opened']; ?></div>
                            <div class="stat-label">Emails Opened</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-number">
                                <?php 
                                $openRate = ($emailStats['total_sent'] > 0) ? round(($emailStats['total_opened'] / $emailStats['total_sent']) * 100) : 0;
                                echo $openRate . '%';
                                ?>
                            </div>
                            <div class="stat-label">Open Rate</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-number">
                                <?php 
                                if (!empty($member['birth_date'])) {
                                    $birthDate = new DateTime($member['birth_date']);
                                    $today = new DateTime();
                                    $nextBirthday = new DateTime($today->format('Y') . '-' . $birthDate->format('m-d'));
                                    if ($nextBirthday < $today) {
                                        $nextBirthday->add(new DateInterval('P1Y'));
                                    }
                                    $daysUntil = $today->diff($nextBirthday)->days;
                                    echo $daysUntil;
                                } else {
                                    echo '-';
                                }
                                ?>
                            </div>
                            <div class="stat-label">Days to Birthday</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- Sidebar -->
                    <div class="col-md-4">
                        <!-- Quick Actions Card -->
                        <div class="modern-card mb-4">
                            <div class="modern-card-header">
                                <h5 class="section-title">
                                    <div class="section-icon">
                                        <i class="bi bi-lightning-charge"></i>
                                    </div>
                                    Quick Actions
                                </h5>
                            </div>
                            <div class="modern-card-body">
                                <div class="d-grid gap-3">
                                    <a href="#" class="action-btn btn btn-primary">
                                        <i class="bi bi-gift me-2"></i>Send Birthday Message
                                    </a>
                                    <a href="#" class="action-btn btn btn-info">
                                        <i class="bi bi-envelope-plus me-2"></i>Send Custom Email
                                    </a>
                                    <a href="#" class="action-btn btn btn-success">
                                        <i class="bi bi-chat-text me-2"></i>Send SMS
                                    </a>
                                    <a href="#" class="action-btn btn btn-warning">
                                        <i class="bi bi-heart me-2"></i>Send Gift
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Engagement Stats -->
                        <div class="modern-card">
                            <div class="modern-card-header">
                                <h5 class="section-title">
                                    <div class="section-icon">
                                        <i class="bi bi-graph-up"></i>
                                    </div>
                                    Engagement Stats
                                </h5>
                            </div>
                            <div class="modern-card-body">
                                <div class="info-item">
                                    <div class="info-icon primary">
                                        <i class="bi bi-envelope-check"></i>
                                    </div>
                                    <div>
                                        <strong>Total Emails Sent</strong><br>
                                        <span class="text-muted"><?php echo number_format($emailStats['total_sent']); ?></span>
                                    </div>
                                </div>

                                <div class="info-item">
                                    <div class="info-icon success">
                                        <i class="bi bi-envelope-open"></i>
                                    </div>
                                    <div>
                                        <strong>Emails Opened</strong><br>
                                        <span class="text-muted"><?php echo number_format($emailStats['total_opened']); ?></span>
                                    </div>
                                </div>

                                <div class="info-item">
                                    <div class="info-icon info">
                                        <i class="bi bi-clock-history"></i>
                                    </div>
                                    <div>
                                        <strong>Last Email Opened</strong><br>
                                        <span class="text-muted"><?php echo date('M d, Y g:i A', strtotime($emailStats['last_opened'])); ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Main Content -->
                    <div class="col-md-8">
                        <!-- Member Information -->
                        <div class="modern-card mb-4">
                            <div class="modern-card-header">
                                <h5 class="section-title">
                                    <div class="section-icon">
                                        <i class="bi bi-person-badge"></i>
                                    </div>
                                    Extended Profile Information
                                </h5>
                            </div>
                            <div class="modern-card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="mb-4" style="color: #667eea; font-weight: 600;">
                                            <i class="bi bi-journal-text me-2"></i>Bio & Interests
                                        </h6>
                                        
                                        <div class="info-item">
                                            <div class="info-icon primary">
                                                <i class="bi bi-journal-text"></i>
                                            </div>
                                            <div>
                                                <strong>Bio</strong><br>
                                                <span class="text-muted">
                                                    <?php if (!empty($member['bio'])): ?>
                                                        <?php echo nl2br(htmlspecialchars(substr($member['bio'], 0, 100))); ?>
                                                        <?php if (strlen($member['bio']) > 100): ?>...<?php endif; ?>
                                                    <?php else: ?>
                                                        Not provided
                                                    <?php endif; ?>
                                                </span>
                                            </div>
                                        </div>

                                        <div class="info-item">
                                            <div class="info-icon success">
                                                <i class="bi bi-star-fill"></i>
                                            </div>
                                            <div>
                                                <strong>Interests & Hobbies</strong><br>
                                                <span class="text-muted">
                                                    <?php if (!empty($member['interests'])): ?>
                                                        <?php echo nl2br(htmlspecialchars(substr($member['interests'], 0, 100))); ?>
                                                        <?php if (strlen($member['interests']) > 100): ?>...<?php endif; ?>
                                                    <?php else: ?>
                                                        Not provided
                                                    <?php endif; ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <h6 class="mb-4" style="color: #667eea; font-weight: 600;">
                                            <i class="bi bi-shield-exclamation me-2"></i>Emergency Contact
                                        </h6>
                                        
                                        <div class="info-item">
                                            <div class="info-icon danger">
                                                <i class="bi bi-person-fill"></i>
                                            </div>
                                            <div>
                                                <strong>Emergency Contact Name</strong><br>
                                                <span class="text-muted">
                                                    <?php echo !empty($member['emergency_contact_name']) ? htmlspecialchars($member['emergency_contact_name']) : 'Not provided'; ?>
                                                </span>
                                            </div>
                                        </div>

                                        <div class="info-item">
                                            <div class="info-icon danger">
                                                <i class="bi bi-telephone-fill"></i>
                                            </div>
                                            <div>
                                                <strong>Emergency Contact Phone</strong><br>
                                                <span class="text-muted">
                                                    <?php if (!empty($member['emergency_contact_phone'])): ?>
                                                        <a href="tel:<?php echo htmlspecialchars($member['emergency_contact_phone']); ?>" class="text-decoration-none">
                                                            <?php echo htmlspecialchars($member['emergency_contact_phone']); ?>
                                                        </a>
                                                    <?php else: ?>
                                                        Not provided
                                                    <?php endif; ?>
                                                </span>
                                            </div>
                                        </div>

                                        <div class="info-item">
                                            <div class="info-icon danger">
                                                <i class="bi bi-heart-fill"></i>
                                            </div>
                                            <div>
                                                <strong>Relationship</strong><br>
                                                <span class="text-muted">
                                                    <?php echo !empty($member['emergency_contact_relationship']) ? htmlspecialchars($member['emergency_contact_relationship']) : 'Not provided'; ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Medical Information -->
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <h6 class="mb-4" style="color: #667eea; font-weight: 600;">
                                            <i class="bi bi-heart-pulse-fill me-2"></i>Medical Information
                                        </h6>
                                        
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="info-item" style="background: linear-gradient(135deg, #fff9e6, #fff3cd); border-left-color: #ffc107;">
                                                    <div class="info-icon warning">
                                                        <i class="bi bi-clipboard-pulse"></i>
                                                    </div>
                                                    <div>
                                                        <strong>Medical Conditions</strong><br>
                                                        <span class="text-muted">
                                                            <?php if (!empty($member['medical_conditions'])): ?>
                                                                <?php echo nl2br(htmlspecialchars(substr($member['medical_conditions'], 0, 100))); ?>
                                                                <?php if (strlen($member['medical_conditions']) > 100): ?>...<?php endif; ?>
                                                            <?php else: ?>
                                                                Not provided
                                                            <?php endif; ?>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="info-item" style="background: linear-gradient(135deg, #ffe6e6, #ffebee); border-left-color: #dc3545;">
                                                    <div class="info-icon danger">
                                                        <i class="bi bi-shield-exclamation"></i>
                                                    </div>
                                                    <div>
                                                        <strong>Allergies</strong><br>
                                                        <span class="text-muted">
                                                            <?php if (!empty($member['allergies'])): ?>
                                                                <?php echo nl2br(htmlspecialchars(substr($member['allergies'], 0, 100))); ?>
                                                                <?php if (strlen($member['allergies']) > 100): ?>...<?php endif; ?>
                                                            <?php else: ?>
                                                                Not provided
                                                            <?php endif; ?>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Important Dates -->
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <h6 class="mb-4" style="color: #667eea; font-weight: 600;">
                                            <i class="bi bi-calendar-heart me-2"></i>Important Dates
                                        </h6>
                                        
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="info-item">
                                                    <div class="info-icon info">
                                                        <i class="bi bi-heart-fill"></i>
                                                    </div>
                                                    <div>
                                                        <strong>Anniversary Date</strong><br>
                                                        <span class="text-muted">
                                                            <?php if (!empty($member['anniversary_date'])): ?>
                                                                <?php echo date('F d, Y', strtotime($member['anniversary_date'])); ?>
                                                            <?php else: ?>
                                                                Not provided
                                                            <?php endif; ?>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="info-item">
                                                    <div class="info-icon primary">
                                                        <i class="bi bi-water"></i>
                                                    </div>
                                                    <div>
                                                        <strong>Baptism Date</strong><br>
                                                        <span class="text-muted">
                                                            <?php if (!empty($member['baptism_date'])): ?>
                                                                <?php echo date('F d, Y', strtotime($member['baptism_date'])); ?>
                                                            <?php else: ?>
                                                                Not provided
                                                            <?php endif; ?>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Volunteer Information -->
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <h6 class="mb-4" style="color: #667eea; font-weight: 600;">
                                            <i class="bi bi-hand-thumbs-up-fill me-2"></i>Volunteer Information
                                        </h6>
                                        
                                        <div class="info-item">
                                            <div class="info-icon success">
                                                <i class="bi bi-hand-thumbs-up-fill"></i>
                                            </div>
                                            <div>
                                                <strong>Volunteer Interests</strong><br>
                                                <span class="text-muted">
                                                    <?php if (!empty($member['volunteer_interests'])): ?>
                                                        <?php echo nl2br(htmlspecialchars($member['volunteer_interests'])); ?>
                                                    <?php else: ?>
                                                        Not provided
                                                    <?php endif; ?>
                                                </span>
                                            </div>
                                        </div>

                                        <div class="info-item">
                                            <div class="info-icon info">
                                                <i class="bi bi-calendar-week"></i>
                                            </div>
                                            <div>
                                                <strong>Availability Schedule</strong><br>
                                                <span class="text-muted">
                                                    <?php if (!empty($member['availability_schedule'])): ?>
                                                        <?php echo nl2br(htmlspecialchars($member['availability_schedule'])); ?>
                                                    <?php else: ?>
                                                        Not provided
                                                    <?php endif; ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
