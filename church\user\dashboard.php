<?php
/**
 * User Dashboard
 * 
 * Main dashboard for authenticated users
 */

// Include configuration first (which sets up session configuration)
require_once '../config.php';

// Start session after configuration is loaded
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include classes
require_once '../classes/SecurityManager.php';
require_once '../classes/UserAuthManager.php';
require_once '../includes/enhanced_activity_system.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Set security headers
$security->setSecurityHeaders();

// Check if user is authenticated
if (!$userAuth->isAuthenticated()) {
    header("Location: login.php");
    exit();
}

$userId = $_SESSION['user_id'];

// Get user data from members table
$stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
$stmt->execute([$userId]);
$userData = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$userData) {
    session_destroy();
    header("Location: login.php");
    exit();
}

// Check if user must change password (redirect to change password page)
if ($userData['must_change_password'] && !isset($_GET['password_changed'])) {
    header("Location: change_password.php");
    exit();
}

// Get site settings for branding
$sitename = get_organization_name() . ' - Member Dashboard';
$stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'site_name'");
$stmt->execute();
$siteNameResult = $stmt->fetch();
if ($siteNameResult) {
    $sitename = $siteNameResult['setting_value'];
}

// Get user preferences
$preferences = $userAuth->getUserPreferences($userId);

// Get upcoming events (next 3 events)
$upcomingEvents = [];
try {
    $stmt = $pdo->prepare("
        SELECT e.*,
               COUNT(CASE WHEN er.status = 'attending' THEN 1 END) as attending_count,
               COUNT(CASE WHEN er.status = 'maybe' THEN 1 END) as maybe_count,
               ur.status as user_rsvp_status
        FROM events e
        LEFT JOIN event_rsvps er ON e.id = er.event_id
        LEFT JOIN event_rsvps ur ON e.id = ur.event_id AND ur.user_id = ?
        WHERE e.is_active = 1
        AND e.event_date > NOW()
        GROUP BY e.id
        ORDER BY e.event_date ASC
        LIMIT 3
    ");
    $stmt->execute([$userId]);
    $upcomingEvents = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Dashboard events error: " . $e->getMessage());
}

// Get today's birthdays
$todaysBirthdays = [];
try {
    $stmt = $pdo->prepare("
        SELECT id, full_name, first_name, last_name, email, image_path,
               COALESCE(birth_date, date_of_birth) as birth_date
        FROM members
        WHERE status = 'active'
        AND id != ?
        AND (
            (MONTH(birth_date) = MONTH(CURDATE()) AND DAY(birth_date) = DAY(CURDATE()))
            OR (MONTH(date_of_birth) = MONTH(CURDATE()) AND DAY(date_of_birth) = DAY(CURDATE()))
        )
        ORDER BY full_name
    ");
    $stmt->execute([$userId]);
    $todaysBirthdays = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Birthday query error: " . $e->getMessage());
    $todaysBirthdays = [];
}

// Get upcoming birthdays (next 7 days)
$upcomingBirthdays = [];
try {
    $stmt = $pdo->prepare("
        SELECT id, full_name, first_name, last_name, email, image_path,
               COALESCE(birth_date, date_of_birth) as birth_date,
               CASE
                   WHEN MONTH(COALESCE(birth_date, date_of_birth)) = MONTH(CURDATE())
                        AND DAY(COALESCE(birth_date, date_of_birth)) > DAY(CURDATE())
                   THEN DAY(COALESCE(birth_date, date_of_birth)) - DAY(CURDATE())
                   WHEN MONTH(COALESCE(birth_date, date_of_birth)) = MONTH(DATE_ADD(CURDATE(), INTERVAL 1 MONTH))
                   THEN DATEDIFF(DATE_ADD(CURDATE(), INTERVAL 1 MONTH), CURDATE()) + DAY(COALESCE(birth_date, date_of_birth))
                   ELSE 0
               END as days_until
        FROM members
        WHERE status = 'active'
        AND id != ?
        AND (
            (MONTH(COALESCE(birth_date, date_of_birth)) = MONTH(CURDATE())
             AND DAY(COALESCE(birth_date, date_of_birth)) > DAY(CURDATE())
             AND DAY(COALESCE(birth_date, date_of_birth)) <= DAY(CURDATE()) + 7)
            OR (MONTH(COALESCE(birth_date, date_of_birth)) = MONTH(DATE_ADD(CURDATE(), INTERVAL 1 MONTH))
                AND DAY(COALESCE(birth_date, date_of_birth)) <= 7 - (DAY(LAST_DAY(CURDATE())) - DAY(CURDATE())))
        )
        ORDER BY days_until, MONTH(COALESCE(birth_date, date_of_birth)), DAY(COALESCE(birth_date, date_of_birth))
        LIMIT 5
    ");
    $stmt->execute([$userId]);
    $upcomingBirthdays = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Upcoming birthdays query error: " . $e->getMessage());
    $upcomingBirthdays = [];
}

// Get recent birthday messages sent by user
$recentBirthdayMessages = [];
try {
    $stmt = $pdo->prepare("
        SELECT ubm.*, m.full_name as recipient_name, bt.name as template_name
        FROM user_birthday_messages ubm
        LEFT JOIN members m ON ubm.recipient_id = m.id
        LEFT JOIN birthday_templates bt ON ubm.template_id = bt.id
        WHERE ubm.sender_id = ?
        ORDER BY ubm.created_at DESC
        LIMIT 5
    ");
    $stmt->execute([$userId]);
    $recentBirthdayMessages = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Birthday messages query error: " . $e->getMessage());
    $recentBirthdayMessages = [];
}



// Get comprehensive recent activities using enhanced activity system
global $activityTracker;
$recentActivity = $activityTracker->getUserRecentActivities($userId, 10);

// Get activity statistics
$activityStats = $activityTracker->getActivityStats($userId);

// Get recent notifications
$recentNotifications = $activityTracker->getUserNotifications($userId, 5);

// Get comprehensive gift activities (sent and received)
$giftActivities = [];
try {
    // Gifts sent by user
    $stmt = $pdo->prepare("
        SELECT mg.*,
               'sent' as activity_type,
               COALESCE(m.full_name, 'Member') as other_member_name
        FROM member_gifts mg
        LEFT JOIN members m ON mg.recipient_id = m.id
        WHERE mg.sender_id = ?
        ORDER BY mg.created_at DESC
        LIMIT 5
    ");
    $stmt->execute([$userId]);
    $sentGifts = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Gifts received by user
    $stmt = $pdo->prepare("
        SELECT mg.*,
               'received' as activity_type,
               CASE
                   WHEN mg.is_anonymous = 1 THEN 'Anonymous'
                   WHEN mg.sender_id IS NULL THEN 'Church Administration'
                   ELSE COALESCE(m.full_name, 'Member')
               END as other_member_name
        FROM member_gifts mg
        LEFT JOIN members m ON mg.sender_id = m.id
        WHERE mg.recipient_id = ?
        ORDER BY mg.created_at DESC
        LIMIT 5
    ");
    $stmt->execute([$userId]);
    $receivedGifts = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Combine and sort by date
    $giftActivities = array_merge($sentGifts, $receivedGifts);
    usort($giftActivities, function($a, $b) {
        return strtotime($b['created_at']) - strtotime($a['created_at']);
    });
    $giftActivities = array_slice($giftActivities, 0, 5);

} catch (PDOException $e) {
    $giftActivities = [];
}

// Get recent admin gifts sent to this user
$adminGifts = [];
try {
    // Check if member_gifts table exists and get admin gifts
    $stmt = $pdo->prepare("
        SELECT mg.*,
               CASE
                   WHEN mg.is_anonymous = 1 THEN 'Church Administration'
                   ELSE COALESCE(m.full_name, 'Church Administration')
               END as sender_name
        FROM member_gifts mg
        LEFT JOIN members m ON mg.sender_id = m.id
        WHERE mg.recipient_id = ?
        AND (mg.sender_id IS NULL OR mg.sender_id IN (
            SELECT id FROM members WHERE role = 'admin' OR email IN (
                SELECT email FROM admins
            )
        ))
        ORDER BY mg.created_at DESC
        LIMIT 3
    ");
    $stmt->execute([$userId]);
    $adminGifts = $stmt->fetchAll();
} catch (PDOException $e) {
    // Table might not exist yet, continue without admin gifts
    $adminGifts = [];
}

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - <?php echo htmlspecialchars($sitename); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Custom Theme CSS from Appearance Settings -->
    <?php include 'includes/theme_css.php'; ?>

    <style>
        body {
            background-color: var(--bs-body-bg, #f8f9fa);
            color: var(--bs-body-color, #212529);
            font-family: var(--bs-font-sans-serif, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif);
        }

        .navbar {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 700;
            color: white !important;
            display: flex;
            align-items: center;
        }

        .navbar-logo {
            max-height: 40px;
            max-width: 150px;
            height: auto;
            width: auto;
            object-fit: contain;
        }

        /* Responsive logo adjustments */
        @media (max-width: 768px) {
            .navbar-logo {
                max-height: 32px;
                max-width: 120px;
            }

            .navbar-brand {
                font-size: 1rem;
            }
        }

        .navbar-nav .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .navbar-nav .nav-link:hover {
            color: white !important;
        }

        .navbar-nav .nav-link.active {
            color: white !important;
            font-weight: 600;
        }

        .dropdown-menu {
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .dropdown-item {
            padding: 0.5rem 1rem;
            transition: background-color 0.15s ease-in-out;
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
        }

        .dropdown-item i {
            width: 16px;
            margin-right: 8px;
        }


        
        .dashboard-container {
            margin-top: 2rem;
        }
        
        .welcome-card {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            color: white;
            border-radius: var(--bs-border-radius, 15px);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
        }
        
        .welcome-card h2 {
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .welcome-card p {
            opacity: 0.9;
            margin: 0;
        }
        
        .dashboard-card {
            background: var(--bs-body-bg, white);
            border-radius: var(--bs-border-radius, 15px);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border: none;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .dashboard-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.12);
        }
        
        .dashboard-card h5 {
            color: var(--bs-body-color, #2c3e50);
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .quick-action-btn {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            border: none;
            border-radius: var(--bs-border-radius, 10px);
            padding: 0.75rem 1.5rem;
            color: white;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin: 0.25rem;
        }
        
        .quick-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
            color: white;
        }
        
        .stat-card {
            text-align: center;
            padding: 1.5rem;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--bs-primary, #667eea);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--bs-secondary, #6c757d);
            font-weight: 500;
            text-transform: uppercase;
            font-size: 0.875rem;
            letter-spacing: 0.5px;
        }

        .stat-mini-card {
            transition: all 0.3s ease;
        }

        .stat-mini-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .stat-mini-card .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .activity-item {
            transition: all 0.3s ease;
        }

        .activity-item:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .activity-icon {
            font-size: 1.2rem;
        }

        .activity-status .badge {
            font-size: 0.75rem;
        }
        
        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid rgba(255,255,255,0.3);
        }
        
        .alert-success {
            border-radius: var(--bs-border-radius, 12px);
            border: none;
            background-color: var(--bs-success, #d4edda);
            color: var(--bs-body-color, #155724);
        }
        
        .btn-outline-light {
            border-color: rgba(255,255,255,0.5);
            color: rgba(255,255,255,0.9);
        }
        
        .btn-outline-light:hover {
            background-color: rgba(255,255,255,0.1);
            border-color: white;
            color: white;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <div class="container dashboard-container">
        <?php if (isset($_GET['password_changed'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="bi bi-check-circle"></i> Your password has been changed successfully! Welcome to your dashboard.
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <!-- Welcome Section -->
        <div class="welcome-card">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2>Welcome back, <?php echo htmlspecialchars($userData['first_name'] ?: $userData['full_name'] ?: 'Member'); ?>!</h2>
                    <p>Here's what's happening in your <?php echo htmlspecialchars(get_organization_name() ?: 'Church'); ?> community today.</p>
                </div>
                <div class="col-md-4 text-end">
                    <?php if (!empty($userData['image_path'])): ?>
                        <img src="../<?php echo htmlspecialchars($userData['image_path']); ?>" alt="Profile" class="profile-avatar">
                    <?php else: ?>
                        <div class="profile-avatar d-inline-flex align-items-center justify-content-center" style="background-color: rgba(255,255,255,0.2);">
                            <i class="bi bi-person-fill" style="font-size: 2rem;"></i>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Upcoming Events Section -->
        <?php if (!empty($upcomingEvents)): ?>
        <div class="row mb-4">
            <div class="col-12">
                <div class="dashboard-card">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0"><i class="bi bi-calendar-event"></i> Upcoming Events</h5>
                        <a href="events.php" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-calendar3"></i> View All Events
                        </a>
                    </div>

                    <div class="row">
                        <?php foreach ($upcomingEvents as $event): ?>
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="event-card border rounded-3 p-3 h-100">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <div class="event-date-badge">
                                        <div class="badge bg-primary">
                                            <div class="fw-bold"><?= date('M j', strtotime($event['event_date'])) ?></div>
                                            <div class="small"><?= date('g:i A', strtotime($event['event_date'])) ?></div>
                                        </div>
                                    </div>
                                    <?php if ($event['max_attendees']): ?>
                                        <small class="text-muted">
                                            <i class="bi bi-people"></i> <?= $event['attending_count'] ?>/<?= $event['max_attendees'] ?>
                                        </small>
                                    <?php endif; ?>
                                </div>

                                <h6 class="event-title mb-2"><?= htmlspecialchars($event['title'] ?? 'Untitled Event') ?></h6>

                                <?php if (!empty($event['location'])): ?>
                                    <p class="text-muted small mb-2">
                                        <i class="bi bi-geo-alt"></i> <?= htmlspecialchars($event['location']) ?>
                                    </p>
                                <?php endif; ?>

                                <p class="event-description small text-muted mb-3">
                                    <?= htmlspecialchars(substr($event['description'], 0, 80)) ?>
                                    <?php if (strlen($event['description']) > 80): ?>...<?php endif; ?>
                                </p>

                                <div class="d-flex justify-content-between align-items-center">
                                    <button type="button" class="btn btn-outline-info btn-sm" onclick="showEventDetails(<?= $event['id'] ?>)">
                                        <i class="bi bi-eye"></i> Details
                                    </button>

                                    <?php if ($event['user_rsvp_status']): ?>
                                        <span class="badge bg-<?= $event['user_rsvp_status'] === 'attending' ? 'success' : ($event['user_rsvp_status'] === 'maybe' ? 'warning' : 'secondary') ?>">
                                            <?= ucfirst(str_replace('_', ' ', $event['user_rsvp_status'])) ?>
                                        </span>
                                    <?php else: ?>
                                        <a href="events.php" class="btn btn-primary btn-sm">
                                            <i class="bi bi-check-circle"></i> RSVP
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Birthday Section -->
        <?php if (!empty($todaysBirthdays) || !empty($upcomingBirthdays)): ?>
        <div class="row mb-4">
            <div class="col-12">
                <div class="dashboard-card">
                    <h5><i class="bi bi-gift"></i> Birthday Celebrations</h5>

                    <?php if (!empty($todaysBirthdays)): ?>
                    <div class="birthday-today mb-4">
                        <h6 class="text-primary"><i class="bi bi-calendar-heart"></i> Today's Birthdays</h6>
                        <div class="row">
                            <?php foreach ($todaysBirthdays as $birthday): ?>
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="birthday-card p-3 border rounded-3 bg-light">
                                    <div class="d-flex align-items-center">
                                        <div class="birthday-avatar me-3">
                                            <?php if (!empty($birthday['image_path'])): ?>
                                                <img src="../<?php echo htmlspecialchars($birthday['image_path']); ?>"
                                                     alt="<?php echo htmlspecialchars($birthday['full_name']); ?>"
                                                     class="rounded-circle" style="width: 50px; height: 50px; object-fit: cover;">
                                            <?php else: ?>
                                                <div class="rounded-circle bg-primary d-flex align-items-center justify-content-center"
                                                     style="width: 50px; height: 50px;">
                                                    <i class="bi bi-person-fill text-white"></i>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1"><?php echo htmlspecialchars($birthday['full_name'] ?? 'Unknown Member'); ?></h6>
                                            <small class="text-muted">🎂 Today is their birthday!</small>
                                        </div>
                                        <div>
                                            <a href="send_birthday_message.php?member_id=<?php echo $birthday['id']; ?>"
                                               class="btn btn-sm btn-primary">
                                                <i class="bi bi-envelope-heart"></i> Send Wishes
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if (!empty($upcomingBirthdays)): ?>
                    <div class="birthday-upcoming">
                        <h6 class="text-info"><i class="bi bi-calendar-week"></i> Upcoming Birthdays (Next 7 Days)</h6>
                        <div class="list-group list-group-flush">
                            <?php foreach ($upcomingBirthdays as $birthday): ?>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <div class="birthday-avatar me-3">
                                        <?php if (!empty($birthday['image_path'])): ?>
                                            <img src="../<?php echo htmlspecialchars($birthday['image_path']); ?>"
                                                 alt="<?php echo htmlspecialchars($birthday['full_name']); ?>"
                                                 class="rounded-circle" style="width: 40px; height: 40px; object-fit: cover;">
                                        <?php else: ?>
                                            <div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center"
                                                 style="width: 40px; height: 40px;">
                                                <i class="bi bi-person-fill text-white"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div>
                                        <h6 class="mb-0"><?php echo htmlspecialchars($birthday['full_name'] ?? 'Unknown Member'); ?></h6>
                                        <small class="text-muted">
                                            <?php
                                            if (!empty($birthday['birth_date'])) {
                                                $birthDate = new DateTime($birthday['birth_date']);
                                                echo $birthDate->format('M j');
                                            }
                                            ?>
                                        </small>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-light text-dark me-2">
                                        <?php echo $birthday['days_until']; ?> day<?php echo $birthday['days_until'] != 1 ? 's' : ''; ?>
                                    </span>
                                    <a href="send_birthday_message.php?member_id=<?php echo $birthday['id']; ?>"
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-envelope"></i>
                                    </a>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <div class="text-center mt-3">
                        <a href="birthday_templates.php" class="btn btn-outline-primary">
                            <i class="bi bi-collection"></i> Browse Birthday Templates
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <div class="row">
            <!-- Quick Actions -->
            <div class="col-md-8">
                <div class="dashboard-card">
                    <h5><i class="bi bi-lightning"></i> Quick Actions</h5>
                    <div class="d-flex flex-wrap">
                        <a href="profile.php" class="quick-action-btn">
                            <i class="bi bi-person-gear"></i> Edit Profile
                        </a>
                        <a href="events.php" class="quick-action-btn">
                            <i class="bi bi-calendar-plus"></i> View Events
                        </a>
                        <a href="settings.php" class="quick-action-btn">
                            <i class="bi bi-gear"></i> Settings
                        </a>
                        <a href="change_password.php" class="quick-action-btn">
                            <i class="bi bi-shield-lock"></i> Change Password
                        </a>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="dashboard-card">
                    <h5><i class="bi bi-activity"></i> Recent Activity</h5>
                    <?php if (empty($recentActivity) && empty($recentBirthdayMessages) && empty($giftActivities)): ?>
                        <p class="text-muted">No recent activity to display.</p>
                        <small class="text-muted">Your activities will appear here as you interact with the system.</small>
                    <?php else: ?>
                        <div class="activity-list">
                            <!-- Gift Activities -->
                            <?php foreach ($giftActivities as $gift): ?>
                            <div class="activity-item d-flex align-items-center mb-3 p-2 bg-light rounded">
                                <div class="activity-icon me-3">
                                    <?php if ($gift['activity_type'] === 'sent'): ?>
                                        <i class="bi bi-gift text-warning"></i>
                                    <?php else: ?>
                                        <i class="bi bi-gift-fill text-success"></i>
                                    <?php endif; ?>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="activity-description">
                                        <?php if ($gift['activity_type'] === 'sent'): ?>
                                            <strong>Gift sent</strong> to <?php echo htmlspecialchars($gift['other_member_name']); ?>
                                        <?php else: ?>
                                            <strong>Gift received</strong> from <?php echo htmlspecialchars($gift['other_member_name']); ?>
                                        <?php endif; ?>
                                        <br><small class="text-muted"><?php echo htmlspecialchars($gift['gift_title']); ?></small>
                                    </div>
                                    <small class="text-muted">
                                        <?php echo date('M j, Y g:i A', strtotime($gift['created_at'])); ?>
                                    </small>
                                </div>
                                <div class="activity-status">
                                    <?php if ($gift['activity_type'] === 'sent'): ?>
                                        <span class="badge bg-warning">Sent</span>
                                    <?php else: ?>
                                        <span class="badge bg-success">Received</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <?php endforeach; ?>


                            <?php foreach ($recentBirthdayMessages as $message): ?>
                            <div class="activity-item d-flex align-items-center mb-3 p-2 bg-light rounded">
                                <div class="activity-icon me-3">
                                    <i class="bi bi-envelope-heart text-primary"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="activity-description">
                                        <strong>Birthday message sent</strong> to <?php echo htmlspecialchars($message['recipient_name'] ?? 'Unknown recipient'); ?>
                                        <?php if (!empty($message['template_name'])): ?>
                                            <small class="text-muted">using "<?php echo htmlspecialchars($message['template_name']); ?>" template</small>
                                        <?php endif; ?>
                                    </div>
                                    <small class="text-muted">
                                        <?php echo date('M j, Y g:i A', strtotime($message['created_at'])); ?>
                                    </small>
                                </div>
                                <div class="activity-status">
                                    <?php if ($message['status'] == 'sent'): ?>
                                        <span class="badge bg-success">Sent</span>
                                    <?php elseif ($message['status'] == 'scheduled'): ?>
                                        <span class="badge bg-warning">Scheduled</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary"><?php echo ucfirst($message['status']); ?></span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <?php endforeach; ?>

                            <!-- Comprehensive Activity Log -->
                            <?php foreach ($recentActivity as $activity): ?>
                            <div class="activity-item d-flex align-items-center mb-3 p-2 bg-light rounded">
                                <div class="activity-icon me-3">
                                    <?php
                                    $icon = 'bi-activity text-secondary';
                                    switch($activity['activity_type']) {
                                        case 'login': $icon = 'bi-box-arrow-in-right text-success'; break;
                                        case 'logout': $icon = 'bi-box-arrow-right text-warning'; break;
                                        case 'profile_update': $icon = 'bi-person-gear text-info'; break;
                                        case 'password_change': $icon = 'bi-shield-lock text-primary'; break;
                                        case 'gift_sent': $icon = 'bi-gift text-warning'; break;
                                        case 'skill_added': $icon = 'bi-plus-circle text-success'; break;
                                        case 'skill_updated': $icon = 'bi-pencil-square text-info'; break;
                                        case 'skill_removed': $icon = 'bi-dash-circle text-danger'; break;
                                        case 'prayer_request_created': $icon = 'bi-chat-heart text-primary'; break;
                                        case 'prayer_response_added': $icon = 'bi-reply text-info'; break;
                                        case 'event_rsvp': $icon = 'bi-calendar-check text-success'; break;
                                        default: $icon = 'bi-activity text-secondary';
                                    }
                                    ?>
                                    <i class="bi <?php echo $icon; ?>"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="activity-description">
                                        <?php echo htmlspecialchars($activity['activity_description'] ?? 'No description available'); ?>
                                        <?php if (!empty($activity['related_member_name'])): ?>
                                            <small class="text-muted d-block">with <?php echo htmlspecialchars($activity['related_member_name']); ?></small>
                                        <?php endif; ?>
                                    </div>
                                    <small class="text-muted">
                                        <?php echo date('M j, Y g:i A', strtotime($activity['created_at'])); ?>
                                    </small>
                                </div>
                                <div class="activity-status">
                                    <?php
                                    $badgeClass = 'bg-secondary';
                                    $statusText = ucfirst(str_replace('_', ' ', $activity['activity_type']));
                                    switch($activity['activity_type']) {
                                        case 'gift_sent': $badgeClass = 'bg-warning'; $statusText = 'Sent'; break;
                                        case 'skill_added': $badgeClass = 'bg-success'; $statusText = 'Added'; break;
                                        case 'skill_updated': $badgeClass = 'bg-info'; $statusText = 'Updated'; break;
                                        case 'skill_removed': $badgeClass = 'bg-danger'; $statusText = 'Removed'; break;
                                        case 'prayer_request_created': $badgeClass = 'bg-primary'; $statusText = 'Created'; break;
                                        case 'prayer_response_added': $badgeClass = 'bg-info'; $statusText = 'Responded'; break;
                                    }
                                    ?>
                                    <span class="badge <?php echo $badgeClass; ?>"><?php echo $statusText; ?></span>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Stats & Info -->
            <div class="col-md-4">
                <!-- Quick Access Cards -->
                <div class="dashboard-card">
                    <h5><i class="bi bi-lightning-charge"></i> Quick Access</h5>
                    <div class="d-grid gap-2">
                        <a href="requests.php" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-chat-heart"></i> Requests
                        </a>
                        <a href="send_gift.php" class="btn btn-outline-warning btn-sm">
                            <i class="bi bi-send"></i> Send Gift
                        </a>
                        <a href="my_gifts.php" class="btn btn-outline-info btn-sm">
                            <i class="bi bi-gift"></i> My Gifts
                        </a>
                        <a href="family_management.php" class="btn btn-outline-info btn-sm">
                            <i class="bi bi-people"></i> Manage Family
                        </a>
                        <a href="volunteer_opportunities.php" class="btn btn-outline-warning btn-sm">
                            <i class="bi bi-person-workspace"></i> Volunteer
                        </a>
                        <a href="skills.php" class="btn btn-outline-info btn-sm">
                            <i class="bi bi-tools"></i> My Skills
                        </a>
                        <a href="profile.php" class="btn btn-outline-success btn-sm">
                            <i class="bi bi-person-gear"></i> Update Profile
                        </a>
                    </div>
                </div>

                <!-- Admin Gifts Section -->
                <?php if (!empty($adminGifts)): ?>
                <div class="dashboard-card">
                    <h5><i class="bi bi-gift"></i> Gifts from Administration</h5>
                    <?php foreach ($adminGifts as $gift): ?>
                        <div class="border-start border-primary border-3 ps-3 mb-3">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1"><?php echo htmlspecialchars($gift['gift_title'] ?? 'Gift'); ?></h6>
                                    <p class="mb-1 small"><?php echo nl2br(htmlspecialchars($gift['gift_message'] ?? '')); ?></p>
                                    <small class="text-muted">
                                        From: <?php echo htmlspecialchars($gift['sender_name'] ?? 'Unknown Sender'); ?> •
                                        <?php echo date('M j, Y', strtotime($gift['created_at'])); ?>
                                    </small>
                                </div>
                                <?php if ($gift['gift_file_path']): ?>
                                    <a href="../<?php echo htmlspecialchars($gift['gift_file_path']); ?>"
                                       class="btn btn-sm btn-outline-primary ms-2" target="_blank">
                                        <i class="bi bi-download"></i>
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                    <div class="text-center">
                        <a href="my_gifts.php" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-inbox"></i> View All Gifts
                        </a>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Activity Statistics -->
                <div class="dashboard-card">
                    <h5><i class="bi bi-graph-up"></i> Your Activity Stats</h5>
                    <div class="row g-2">
                        <div class="col-6">
                            <div class="stat-mini-card text-center p-2 bg-light rounded">
                                <div class="stat-number text-warning"><?php echo $activityStats['gifts_sent'] ?? 0; ?></div>
                                <div class="stat-label small">Gifts Sent</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-mini-card text-center p-2 bg-light rounded">
                                <div class="stat-number text-success"><?php echo $activityStats['gifts_received'] ?? 0; ?></div>
                                <div class="stat-label small">Gifts Received</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-mini-card text-center p-2 bg-light rounded">
                                <div class="stat-number text-info"><?php echo $activityStats['skills_count'] ?? 0; ?></div>
                                <div class="stat-label small">Skills</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-mini-card text-center p-2 bg-light rounded">
                                <div class="stat-number text-primary"><?php echo $activityStats['prayer_requests'] ?? 0; ?></div>
                                <div class="stat-label small">Prayer Requests</div>
                            </div>
                        </div>
                    </div>
                    <?php if (($activityStats['unread_notifications'] ?? 0) > 0): ?>
                    <div class="mt-3 text-center">
                        <a href="notifications.php" class="btn btn-sm btn-outline-danger">
                            <i class="bi bi-bell"></i> <?php echo $activityStats['unread_notifications']; ?> Unread Notifications
                        </a>
                    </div>
                    <?php endif; ?>
                </div>

                <div class="dashboard-card stat-card">
                    <div class="stat-number"><?php echo count($todaysBirthdays); ?></div>
                    <div class="stat-label">Today's Birthdays</div>
                </div>

                <div class="dashboard-card stat-card">
                    <div class="stat-number"><?php echo count($recentBirthdayMessages); ?></div>
                    <div class="stat-label">Birthday Messages Sent</div>
                </div>

                <div class="dashboard-card">
                    <h5><i class="bi bi-info-circle"></i> Account Info</h5>
                    <p><strong>Email:</strong> <?php echo htmlspecialchars($userData['email'] ?? 'Not provided'); ?></p>
                    <?php if (!empty($userData['phone_number'])): ?>
                        <p><strong>Phone:</strong> <?php echo htmlspecialchars($userData['phone_number']); ?></p>
                    <?php endif; ?>
                    <p><strong>Member since:</strong> <?php echo date('M Y', strtotime($userData['created_at'])); ?></p>
                    <?php if (!empty($userData['last_login_at'])): ?>
                        <p><strong>Last login:</strong> <?php echo date('M j, Y g:i A', strtotime($userData['last_login_at'])); ?></p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Event Details Modal -->
    <div class="modal fade" id="eventDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="eventDetailsTitle">Event Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="eventDetailsBody">
                    <!-- Event details will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="events.php" class="btn btn-primary">Go to Events</a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert-dismissible');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);

        function showEventDetails(eventId) {
            // Show loading state
            document.getElementById('eventDetailsBody').innerHTML = '<div class="text-center"><div class="spinner-border" role="status"><span class="visually-hidden">Loading...</span></div></div>';

            // Show modal
            new bootstrap.Modal(document.getElementById('eventDetailsModal')).show();

            // Fetch event details
            fetch('get_event_details.php?id=' + eventId)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('eventDetailsTitle').textContent = data.event.title;
                        document.getElementById('eventDetailsBody').innerHTML = data.html;
                    } else {
                        document.getElementById('eventDetailsBody').innerHTML = '<div class="alert alert-danger">Error loading event details: ' + data.message + '</div>';
                    }
                })
                .catch(error => {
                    document.getElementById('eventDetailsBody').innerHTML = '<div class="alert alert-danger">Error loading event details.</div>';
                });
        }
    </script>

    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>
</body>
</html>
